import React from "react";
import { withAuth } from "@/lib/auth";
import Layout from "@/components/Layout/Layout";
import { CalendarIcon, ClockIcon } from "@heroicons/react/24/outline";

const SchedulesPage: React.FC = () => {
  return (
    <Layout title="Lịch hẹn">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-elderly-text">
            Lịch hẹn & Nhắc nhở
          </h1>
          <button className="btn btn-primary">Thêm lịch hẹn</button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Today's Schedule */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <CalendarIcon className="h-5 w-5 mr-2 text-primary-600" />
              Lịch hôm nay
            </h2>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-blue-800">Khám định kỳ</h3>
                    <p className="text-blue-600 text-sm">Bác sĩ Nguyễn Văn A</p>
                    <p className="text-blue-600 text-sm">Bệnh viện ABC</p>
                  </div>
                  <span className="text-blue-600 font-medium">14:00</span>
                </div>
              </div>

              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-green-800">
                      Uống thuốc huyết áp
                    </h3>
                    <p className="text-green-600 text-sm">Lisinopril 10mg</p>
                  </div>
                  <span className="text-green-600 font-medium">20:00</span>
                </div>
              </div>
            </div>
          </div>

          {/* Upcoming Reminders */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <ClockIcon className="h-5 w-5 mr-2 text-primary-600" />
              Nhắc nhở sắp tới
            </h2>
            <div className="space-y-3">
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-yellow-800">
                      Uống thuốc tiểu đường
                    </h3>
                    <p className="text-yellow-600 text-sm">Metformin 500mg</p>
                  </div>
                  <span className="text-yellow-600 font-medium">
                    Ngày mai 8:00
                  </span>
                </div>
              </div>

              <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-purple-800">
                      Tái khám tim mạch
                    </h3>
                    <p className="text-purple-600 text-sm">Bác sĩ Trần Thị B</p>
                  </div>
                  <span className="text-purple-600 font-medium">
                    Thứ 6 tuần sau
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Calendar View */}
        <div className="mt-8">
          <div className="card">
            <h2 className="text-xl font-semibold mb-4">Lịch tháng</h2>
            <div className="bg-gray-100 p-8 rounded-lg text-center">
              <p className="text-elderly-text-light">
                Giao diện lịch sẽ được phát triển ở phiên bản tiếp theo
              </p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default withAuth(SchedulesPage);
