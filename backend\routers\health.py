"""
Health records API routes for Elderly Health Support System
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from typing import List, Optional
from pydantic import BaseModel, validator
from datetime import datetime, date, timedelta
import logging

from database import get_database
from auth import get_current_user
from models.user import User
from models.health import HealthRecord, RecordTypeEnum

# Logging setup
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/health", tags=["health"])

# Pydantic models
class HealthRecordCreate(BaseModel):
    record_type: str
    systolic_pressure: Optional[int] = None
    diastolic_pressure: Optional[int] = None
    heart_rate: Optional[int] = None
    blood_sugar: Optional[float] = None
    weight: Optional[float] = None
    temperature: Optional[float] = None
    notes: Optional[str] = None
    recorded_at: Optional[datetime] = None
    
    @validator('record_type')
    def validate_record_type(cls, v):
        if v not in ['blood_pressure', 'heart_rate', 'blood_sugar', 'weight', 'temperature']:
            raise ValueError('Invalid record type')
        return v
    
    @validator('systolic_pressure', 'diastolic_pressure')
    def validate_blood_pressure(cls, v, values):
        if values.get('record_type') == 'blood_pressure' and v is None:
            raise ValueError('Blood pressure values are required for blood pressure records')
        if v is not None and (v < 50 or v > 300):
            raise ValueError('Blood pressure value out of valid range')
        return v
    
    @validator('heart_rate')
    def validate_heart_rate(cls, v, values):
        if values.get('record_type') == 'heart_rate' and v is None:
            raise ValueError('Heart rate is required for heart rate records')
        if v is not None and (v < 30 or v > 200):
            raise ValueError('Heart rate value out of valid range')
        return v
    
    @validator('blood_sugar')
    def validate_blood_sugar(cls, v, values):
        if values.get('record_type') == 'blood_sugar' and v is None:
            raise ValueError('Blood sugar is required for blood sugar records')
        if v is not None and (v < 20 or v > 600):
            raise ValueError('Blood sugar value out of valid range')
        return v
    
    @validator('weight')
    def validate_weight(cls, v, values):
        if values.get('record_type') == 'weight' and v is None:
            raise ValueError('Weight is required for weight records')
        if v is not None and (v < 20 or v > 300):
            raise ValueError('Weight value out of valid range')
        return v
    
    @validator('temperature')
    def validate_temperature(cls, v, values):
        if values.get('record_type') == 'temperature' and v is None:
            raise ValueError('Temperature is required for temperature records')
        if v is not None and (v < 30 or v > 45):
            raise ValueError('Temperature value out of valid range')
        return v

class HealthRecordResponse(BaseModel):
    id: int
    user_id: int
    record_type: str
    systolic_pressure: Optional[int]
    diastolic_pressure: Optional[int]
    heart_rate: Optional[int]
    blood_sugar: Optional[float]
    weight: Optional[float]
    temperature: Optional[float]
    notes: Optional[str]
    recorded_at: datetime
    created_at: datetime
    display_value: str
    is_normal: bool

class HealthStatsResponse(BaseModel):
    record_type: str
    total_records: int
    latest_value: Optional[str]
    latest_date: Optional[datetime]
    average_last_7_days: Optional[float]
    average_last_30_days: Optional[float]
    trend: str  # "improving", "stable", "declining"

@router.post("/records", response_model=HealthRecordResponse, status_code=status.HTTP_201_CREATED)
async def create_health_record(
    record_data: HealthRecordCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Create a new health record
    """
    try:
        auth0_id = current_user.get("sub")
        
        user = db.query(User).filter(User.auth0_id == auth0_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Create health record
        health_record = HealthRecord(
            user_id=user.id,
            record_type=RecordTypeEnum(record_data.record_type),
            systolic_pressure=record_data.systolic_pressure,
            diastolic_pressure=record_data.diastolic_pressure,
            heart_rate=record_data.heart_rate,
            blood_sugar=record_data.blood_sugar,
            weight=record_data.weight,
            temperature=record_data.temperature,
            notes=record_data.notes,
            recorded_at=record_data.recorded_at or datetime.now()
        )
        
        db.add(health_record)
        db.commit()
        db.refresh(health_record)
        
        logger.info(f"Health record created: {health_record.id} for user {user.id}")
        
        # Prepare response
        record_dict = health_record.to_dict()
        record_dict['display_value'] = health_record.get_display_value()
        record_dict['is_normal'] = health_record.is_normal_range()
        
        return HealthRecordResponse(**record_dict)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating health record: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create health record"
        )

@router.get("/records", response_model=List[HealthRecordResponse])
async def get_health_records(
    record_type: Optional[str] = Query(None, description="Filter by record type"),
    limit: int = Query(50, ge=1, le=100, description="Number of records to return"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
    start_date: Optional[date] = Query(None, description="Start date filter"),
    end_date: Optional[date] = Query(None, description="End date filter"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Get user's health records with filtering and pagination
    """
    try:
        auth0_id = current_user.get("sub")
        
        user = db.query(User).filter(User.auth0_id == auth0_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Build query
        query = db.query(HealthRecord).filter(HealthRecord.user_id == user.id)
        
        # Apply filters
        if record_type:
            query = query.filter(HealthRecord.record_type == RecordTypeEnum(record_type))
        
        if start_date:
            query = query.filter(HealthRecord.recorded_at >= start_date)
        
        if end_date:
            query = query.filter(HealthRecord.recorded_at <= end_date)
        
        # Order by recorded_at descending
        query = query.order_by(desc(HealthRecord.recorded_at))
        
        # Apply pagination
        records = query.offset(offset).limit(limit).all()
        
        # Prepare response
        response_records = []
        for record in records:
            record_dict = record.to_dict()
            record_dict['display_value'] = record.get_display_value()
            record_dict['is_normal'] = record.is_normal_range()
            response_records.append(HealthRecordResponse(**record_dict))
        
        return response_records
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting health records: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get health records"
        )

@router.get("/records/{record_id}", response_model=HealthRecordResponse)
async def get_health_record(
    record_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Get a specific health record
    """
    try:
        auth0_id = current_user.get("sub")
        
        user = db.query(User).filter(User.auth0_id == auth0_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        record = db.query(HealthRecord).filter(
            HealthRecord.id == record_id,
            HealthRecord.user_id == user.id
        ).first()
        
        if not record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Health record not found"
            )
        
        record_dict = record.to_dict()
        record_dict['display_value'] = record.get_display_value()
        record_dict['is_normal'] = record.is_normal_range()
        
        return HealthRecordResponse(**record_dict)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting health record: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get health record"
        )

@router.delete("/records/{record_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_health_record(
    record_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Delete a health record
    """
    try:
        auth0_id = current_user.get("sub")
        
        user = db.query(User).filter(User.auth0_id == auth0_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        record = db.query(HealthRecord).filter(
            HealthRecord.id == record_id,
            HealthRecord.user_id == user.id
        ).first()
        
        if not record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Health record not found"
            )
        
        db.delete(record)
        db.commit()
        
        logger.info(f"Health record deleted: {record_id} for user {user.id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting health record: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete health record"
        )
