"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: Inter, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n  html {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\n\\n  body {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 254 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n    font-size: 18px;\\n    /* Larger base font size for elderly users */\\n    line-height: 1.6;\\n}\\n\\n  /* Focus styles for accessibility */\\n  *:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n  /* High contrast mode support */\\n  @media (prefers-contrast: high) {\\n    body {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n  }\\n  }\\n\\n  /* Reduced motion support */\\n  @media (prefers-reduced-motion: reduce) {\\n    * {\\n      animation-duration: 0.01ms !important;\\n      animation-iteration-count: 1 !important;\\n      transition-duration: 0.01ms !important;\\n    }\\n  }\\n.form-input,.form-textarea,.form-select,.form-multiselect {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n.form-input:focus, .form-textarea:focus, .form-select:focus, .form-multiselect:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n.form-input::-moz-placeholder, .form-textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n.form-input::placeholder,.form-textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n.form-input::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n.form-input::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n.form-input::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n.form-input::-webkit-datetime-edit,.form-input::-webkit-datetime-edit-year-field,.form-input::-webkit-datetime-edit-month-field,.form-input::-webkit-datetime-edit-day-field,.form-input::-webkit-datetime-edit-hour-field,.form-input::-webkit-datetime-edit-minute-field,.form-input::-webkit-datetime-edit-second-field,.form-input::-webkit-datetime-edit-millisecond-field,.form-input::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n.form-select {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n}\\n.form-select:where([size]:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  -webkit-print-color-adjust: unset;\\n          print-color-adjust: unset;\\n}\\n/* Button styles */\\n.btn {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding: 1rem 1.5rem;\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n  font-weight: 500;\\n  border-radius: 0.5rem;\\n  transition: all 0.2s ease-in-out;\\n}\\n.btn:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n}\\n.btn:disabled {\\n  cursor: not-allowed;\\n  opacity: 0.5;\\n}\\n.btn-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));\\n}\\n.btn-primary:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\\n}\\n.btn-secondary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(15 23 42 / var(--tw-text-opacity, 1));\\n}\\n.btn-secondary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));\\n}\\n.btn-secondary:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));\\n}\\n.btn-outline {\\n  border-width: 2px;\\n  border-color: currentColor;\\n  background-color: transparent;\\n}\\n.btn-outline:hover {\\n  background-color: currentColor;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-lg {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n/* Card styles */\\n.card {\\n  transition-property: box-shadow;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  background-color: #ffffff;\\n  border-radius: 1rem;\\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n  border: 1px solid #e5e7eb;\\n  padding: 1.5rem;\\n}\\n.card:hover {\\n  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n/* Form styles */\\n.form-input {\\n  width: 100%;\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n  padding: 1rem;\\n  border-radius: 0.5rem;\\n  border: 2px solid #e5e7eb;\\n}\\n.form-input:focus {\\n  border-color: #0ea5e9;\\n  box-shadow: 0 0 0 3px #e0f2fe;\\n}\\n.form-select {\\n  width: 100%;\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n  padding: 1rem;\\n  border-radius: 0.5rem;\\n  border: 2px solid #e5e7eb;\\n}\\n.form-select:focus {\\n  border-color: #0ea5e9;\\n  box-shadow: 0 0 0 3px #e0f2fe;\\n}\\n.form-textarea {\\n  width: 100%;\\n  resize: vertical;\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n  padding: 1rem;\\n  border-radius: 0.5rem;\\n  border: 2px solid #e5e7eb;\\n}\\n.form-textarea:focus {\\n  border-color: #0ea5e9;\\n  box-shadow: 0 0 0 3px #e0f2fe;\\n}\\n/* Navigation styles */\\n.nav-link {\\n  border-radius: 0.5rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n  font-weight: 500;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.nav-link-active {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-inactive {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-inactive:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n/* Health status indicators */\\n/* Loading states */\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.loading-spinner {\\n  animation: spin 1s linear infinite;\\n  border-radius: 9999px;\\n  border-width: 4px;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(2 132 199 / var(--tw-border-opacity, 1));\\n}\\n/* Toast notifications */\\n.toast {\\n  margin-bottom: 1rem;\\n  border-radius: 0.5rem;\\n  border-left-width: 4px;\\n  padding: 1rem;\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n/* Chart containers */\\n/* Responsive text sizes */\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.sticky {\\n  position: sticky;\\n}\\n.inset-x-0 {\\n  left: 0px;\\n  right: 0px;\\n}\\n.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.-right-1 {\\n  right: -0.25rem;\\n}\\n.-top-1 {\\n  top: -0.25rem;\\n}\\n.-top-40 {\\n  top: -10rem;\\n}\\n.left-\\\\[calc\\\\(50\\\\%\\\\+3rem\\\\)\\\\] {\\n  left: calc(50% + 3rem);\\n}\\n.left-\\\\[calc\\\\(50\\\\%-11rem\\\\)\\\\] {\\n  left: calc(50% - 11rem);\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.top-0 {\\n  top: 0px;\\n}\\n.top-\\\\[calc\\\\(100\\\\%-13rem\\\\)\\\\] {\\n  top: calc(100% - 13rem);\\n}\\n.isolate {\\n  isolation: isolate;\\n}\\n.-z-10 {\\n  z-index: -10;\\n}\\n.z-10 {\\n  z-index: 10;\\n}\\n.z-40 {\\n  z-index: 40;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.order-first {\\n  order: -9999;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.-ml-2 {\\n  margin-left: -0.5rem;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-12 {\\n  margin-bottom: 3rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-10 {\\n  margin-top: 2.5rem;\\n}\\n.mt-16 {\\n  margin-top: 4rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.mt-auto {\\n  margin-top: auto;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\n.block {\\n  display: block;\\n}\\n.inline-block {\\n  display: inline-block;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.aspect-\\\\[1155\\\\/678\\\\] {\\n  aspect-ratio: 1155/678;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-16 {\\n  height: 4rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.h-screen {\\n  height: 100vh;\\n}\\n.h-3 {\\n  height: 0.75rem;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.min-h-96 {\\n  min-height: 24rem;\\n}\\n.w-11 {\\n  width: 2.75rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-56 {\\n  width: 14rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-\\\\[36\\\\.125rem\\\\] {\\n  width: 36.125rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.w-3 {\\n  width: 0.75rem;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\n.max-w-7xl {\\n  max-width: 80rem;\\n}\\n.max-w-xl {\\n  max-width: 36rem;\\n}\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.flex-auto {\\n  flex: 1 1 auto;\\n}\\n.flex-none {\\n  flex: none;\\n}\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.origin-top-right {\\n  transform-origin: top right;\\n}\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rotate-\\\\[30deg\\\\] {\\n  --tw-rotate: 30deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-100 {\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.scale-95 {\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform-gpu {\\n  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-start {\\n  justify-content: flex-start;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-0\\\\.5 {\\n  gap: 0.125rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.gap-8 {\\n  gap: 2rem;\\n}\\n.gap-x-3 {\\n  -moz-column-gap: 0.75rem;\\n       column-gap: 0.75rem;\\n}\\n.gap-x-6 {\\n  -moz-column-gap: 1.5rem;\\n       column-gap: 1.5rem;\\n}\\n.gap-x-8 {\\n  -moz-column-gap: 2rem;\\n       column-gap: 2rem;\\n}\\n.gap-y-16 {\\n  row-gap: 4rem;\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.whitespace-pre-line {\\n  white-space: pre-line;\\n}\\n.rounded-2xl {\\n  border-radius: 1.5rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\n.border-r {\\n  border-right-width: 1px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-t-4 {\\n  border-top-width: 4px;\\n}\\n.border-blue-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n}\\n.border-elderly-border {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(2 132 199 / var(--tw-border-opacity, 1));\\n}\\n.border-purple-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\\n}\\n.border-red-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\\n}\\n.border-transparent {\\n  border-color: transparent;\\n}\\n.border-white {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n}\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.bg-blue-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-elderly-bg {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 254 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-elderly-hover-bg {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gradient-to-tr {\\n  background-image: linear-gradient(to top right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-primary-400 {\\n  --tw-gradient-from: #38bdf8 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(56 189 248 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-50 {\\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500 {\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-primary-600 {\\n  --tw-gradient-to: #0284c7 var(--tw-gradient-to-position);\\n}\\n.to-green-50 {\\n  --tw-gradient-to: #f0fdf4 var(--tw-gradient-to-position);\\n}\\n.to-green-500 {\\n  --tw-gradient-to: #22c55e var(--tw-gradient-to-position);\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-16 {\\n  padding-top: 4rem;\\n  padding-bottom: 4rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-24 {\\n  padding-top: 6rem;\\n  padding-bottom: 6rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-32 {\\n  padding-top: 8rem;\\n  padding-bottom: 8rem;\\n}\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.pr-12 {\\n  padding-right: 3rem;\\n}\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.pt-14 {\\n  padding-top: 3.5rem;\\n}\\n.pt-8 {\\n  padding-top: 2rem;\\n}\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-right {\\n  text-align: right;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.leading-6 {\\n  line-height: 1.5rem;\\n}\\n.leading-7 {\\n  line-height: 1.75rem;\\n}\\n.leading-8 {\\n  line-height: 2rem;\\n}\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-elderly-text {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-elderly-text-light {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-green-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(224 242 254 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\\n}\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-red-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.underline {\\n  text-decoration-line: underline;\\n}\\n.opacity-0 {\\n  opacity: 0;\\n}\\n.opacity-100 {\\n  opacity: 1;\\n}\\n.opacity-30 {\\n  opacity: 0.3;\\n}\\n.opacity-75 {\\n  opacity: 0.75;\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-soft {\\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.ring-1 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.ring-black {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));\\n}\\n.ring-opacity-5 {\\n  --tw-ring-opacity: 0.05;\\n}\\n.blur-3xl {\\n  --tw-blur: blur(64px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-shadow {\\n  transition-property: box-shadow;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-100 {\\n  transition-duration: 100ms;\\n}\\n.duration-75 {\\n  transition-duration: 75ms;\\n}\\n.ease-in {\\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\\n}\\n.ease-out {\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\n/* Accessibility utilities */\\n.sr-only {\\n    position: absolute;\\n    width: 1px;\\n    height: 1px;\\n    padding: 0;\\n    margin: -1px;\\n    overflow: hidden;\\n    clip: rect(0, 0, 0, 0);\\n    white-space: nowrap;\\n    border: 0;\\n  }\\n/* Print styles */\\n@media print {\\n    .no-print {\\n      display: none !important;\\n    }\\n\\n    .print-only {\\n      display: block !important;\\n    }\\n\\n    body {\\n      font-size: 12pt;\\n      line-height: 1.4;\\n    }\\n\\n    .card {\\n      box-shadow: none;\\n      border: 1px solid #ccc;\\n    }\\n  }\\n/* High contrast mode utilities */\\n/* Animation utilities */\\n/* Spacing utilities for elderly-friendly design */\\n\\n/* Import Google Fonts */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\\n\\n/* Base styles for elderly-friendly design */\\n\\n/* Component styles */\\n\\n/* Utility styles */\\n\\n/* Custom scrollbar for better UX */\\n::-webkit-scrollbar {\\n  width: 12px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n\\n/* Selection styles */\\n::-moz-selection {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(186 230 253 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(12 74 110 / var(--tw-text-opacity, 1));\\n}\\n::selection {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(186 230 253 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(12 74 110 / var(--tw-text-opacity, 1));\\n}\\n\\n/* Focus visible for better keyboard navigation */\\n.focus-visible:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n}\\n.after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\n.after\\\\:left-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 2px;\\n}\\n.after\\\\:top-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  top: 2px;\\n}\\n.after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\n.after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\n.after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\n.after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\n.after\\\\:border-gray-300::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\n.hover\\\\:bg-elderly-hover-bg:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-primary-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-primary-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:text-primary-100:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(224 242 254 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-primary-500:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(14 165 233 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-primary-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-red-800:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:shadow-card-hover:hover {\\n  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.focus\\\\:not-sr-only:focus {\\n  position: static;\\n  width: auto;\\n  height: auto;\\n  padding: 0;\\n  margin: 0;\\n  overflow: visible;\\n  clip: auto;\\n  white-space: normal;\\n}\\n.focus\\\\:absolute:focus {\\n  position: absolute;\\n}\\n.focus\\\\:left-4:focus {\\n  left: 1rem;\\n}\\n.focus\\\\:top-4:focus {\\n  top: 1rem;\\n}\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-primary-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n.focus-visible\\\\:outline:focus-visible {\\n  outline-style: solid;\\n}\\n.focus-visible\\\\:outline-2:focus-visible {\\n  outline-width: 2px;\\n}\\n.focus-visible\\\\:outline-offset-2:focus-visible {\\n  outline-offset: 2px;\\n}\\n.focus-visible\\\\:outline-white:focus-visible {\\n  outline-color: #fff;\\n}\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\n.peer:checked ~ .peer-checked\\\\:bg-primary-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.peer:focus ~ .peer-focus\\\\:outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-4 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-primary-300 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(125 211 252 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:-top-80 {\\n    top: -20rem;\\n  }\\n\\n  .sm\\\\:left-\\\\[calc\\\\(50\\\\%\\\\+36rem\\\\)\\\\] {\\n    left: calc(50% + 36rem);\\n  }\\n\\n  .sm\\\\:left-\\\\[calc\\\\(50\\\\%-30rem\\\\)\\\\] {\\n    left: calc(50% - 30rem);\\n  }\\n\\n  .sm\\\\:top-\\\\[calc\\\\(100\\\\%-30rem\\\\)\\\\] {\\n    top: calc(100% - 30rem);\\n  }\\n\\n  .sm\\\\:mx-auto {\\n    margin-left: auto;\\n    margin-right: auto;\\n  }\\n\\n  .sm\\\\:mt-20 {\\n    margin-top: 5rem;\\n  }\\n\\n  .sm\\\\:block {\\n    display: block;\\n  }\\n\\n  .sm\\\\:w-\\\\[72\\\\.1875rem\\\\] {\\n    width: 72.1875rem;\\n  }\\n\\n  .sm\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .sm\\\\:max-w-md {\\n    max-width: 28rem;\\n  }\\n\\n  .sm\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:rounded-lg {\\n    border-radius: 0.5rem;\\n  }\\n\\n  .sm\\\\:px-10 {\\n    padding-left: 2.5rem;\\n    padding-right: 2.5rem;\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .sm\\\\:py-32 {\\n    padding-top: 8rem;\\n    padding-bottom: 8rem;\\n  }\\n\\n  .sm\\\\:py-48 {\\n    padding-top: 12rem;\\n    padding-bottom: 12rem;\\n  }\\n\\n  .sm\\\\:text-4xl {\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n\\n  .sm\\\\:text-6xl {\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .md\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .md\\\\:block {\\n    display: block;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:ml-0 {\\n    margin-left: 0px;\\n  }\\n\\n  .lg\\\\:ml-8 {\\n    margin-left: 2rem;\\n  }\\n\\n  .lg\\\\:mt-24 {\\n    margin-top: 6rem;\\n  }\\n\\n  .lg\\\\:block {\\n    display: block;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:max-w-md {\\n    max-width: 28rem;\\n  }\\n\\n  .lg\\\\:max-w-none {\\n    max-width: none;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n\\n  .lg\\\\:py-56 {\\n    padding-top: 14rem;\\n    padding-bottom: 14rem;\\n  }\\n\\n  .lg\\\\:text-center {\\n    text-align: center;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,yCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;EAAd;IAAA,2CAAc;IAAd,uBAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd,4DAAc;EAAd,oBAAc;EAAd,gDAAc;EAAd,mCAAc;EAAd,kCAAc;IAAd,eAAc;IAAd,4CAAc;IAAd;AAAc;;EAAd,mCAAc;EAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,4FAAc;EAAd,oBAAc;EAAd,4DAAc;EAAd;AAAc;;EAAd,+BAAc;EAAd;IAAA;IAAA,kBAAc;IAAd,4DAAc;IAAd,oBAAc;IAAd;EAAc;EAAA;;EAAd,2BAAc;EAAd;IAAA;MAAA,qCAAc;MAAd,uCAAc;MAAd,sCAAc;IAAA;EAAA;AACd;EAAA,wBAAoB;KAApB,qBAAoB;UAApB,gBAAoB;EAApB,sBAAoB;EAApB,qBAAoB;EAApB,iBAAoB;EAApB,kBAAoB;EAApB,mBAAoB;EAApB,sBAAoB;EAApB,sBAAoB;EAApB,qBAAoB;EAApB,eAAoB;EAApB,mBAAoB;EAApB,sBAAoB;AAAA;AAApB;EAAA,8BAAoB;EAApB,mBAAoB;EAApB,4CAAoB;EAApB,2BAAoB;EAApB,4BAAoB;EAApB,wBAAoB;EAApB,2GAAoB;EAApB,yGAAoB;EAApB,iFAAoB;EAApB;AAAoB;AAApB;EAAA,cAAoB;EAApB;AAAoB;AAApB;EAAA,cAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,iBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,cAAoB;EAApB;AAAoB;AAApB;EAAA,mPAAoB;EAApB,wCAAoB;EAApB,4BAAoB;EAApB,4BAAoB;EAApB,qBAAoB;EAApB,iCAAoB;UAApB;AAAoB;AAApB;EAAA,yBAAoB;EAApB,4BAAoB;EAApB,wBAAoB;EAApB,wBAAoB;EAApB,sBAAoB;EAApB,iCAAoB;UAApB;AAAoB;AA6ClB,kBAAkB;AAEhB;EAAA,oBAAoM;EAApM,mBAAoM;EAApM,uBAAoM;EAApM,iBAAoM;EAApM,yBAAoM;EAApM,oBAAoM;EAApM,mBAAoM;EAApM,oBAAoM;EAApM,gBAAoM;EAApM,qBAAoM;EAApM;AAAoM;AAApM;EAAA,8BAAoM;EAApM,mBAAoM;EAApM,2GAAoM;EAApM,yGAAoM;EAApM,4FAAoM;EAApM;AAAoM;AAApM;EAAA,mBAAoM;EAApM;AAAoM;AAIpM;EAAA,kBAA4E;EAA5E,0DAA4E;EAA5E,oBAA4E;EAA5E;AAA4E;AAA5E;EAAA,kBAA4E;EAA5E;AAA4E;AAA5E;EAAA,oBAA4E;EAA5E;AAA4E;AAI5E;EAAA,kBAA0F;EAA1F,4DAA0F;EAA1F,oBAA0F;EAA1F;AAA0F;AAA1F;EAAA,kBAA0F;EAA1F;AAA0F;AAA1F;EAAA,oBAA0F;EAA1F;AAA0F;AAgB1F;EAAA,iBAA+E;EAA/E,0BAA+E;EAA/E;AAA+E;AAA/E;EAAA,8BAA+E;EAA/E,oBAA+E;EAA/E;AAA+E;AAI/E;EAAA,kBAAwB;EAAxB,mBAAwB;EAAxB,iBAAwB;EAAxB,oBAAwB;EAAxB,kBAAwB;EAAxB;AAAwB;AAO1B,gBAAgB;AAEd;EAAA,+BAAkD;EAAlD,wDAAkD;EAAlD,0BAAkD;EAAlD,yBAAkD;EAAlD,mBAAkD;EAAlD,2EAAkD;EAAlD,yBAAkD;EAAlD;AAAkD;AAIlD;EAAA,kFAAwB;EAAxB,iGAAwB;EAAxB;AAAwB;AAe1B,gBAAgB;AAUd;EAAA,WAA2B;EAA3B,mBAA2B;EAA3B,oBAA2B;EAA3B,aAA2B;EAA3B,qBAA2B;EAA3B;AAA2B;AAA3B;EAAA,qBAA2B;EAA3B;AAA2B;AAI3B;EAAA,WAA2B;EAA3B,mBAA2B;EAA3B,oBAA2B;EAA3B,aAA2B;EAA3B,qBAA2B;EAA3B;AAA2B;AAA3B;EAAA,qBAA2B;EAA3B;AAA2B;AAI3B;EAAA,WAAoC;EAApC,gBAAoC;EAApC,mBAAoC;EAApC,oBAAoC;EAApC,aAAoC;EAApC,qBAAoC;EAApC;AAAoC;AAApC;EAAA,qBAAoC;EAApC;AAAoC;AAWtC,sBAAsB;AAEpB;EAAA,qBAA8E;EAA9E,kBAA8E;EAA9E,mBAA8E;EAA9E,oBAA8E;EAA9E,uBAA8E;EAA9E,mBAA8E;EAA9E,oBAA8E;EAA9E,gBAA8E;EAA9E,+FAA8E;EAA9E,wDAA8E;EAA9E;AAA8E;AAI9E;EAAA,kBAAsC;EAAtC,4DAAsC;EAAtC,oBAAsC;EAAtC;AAAsC;AAItC;EAAA,oBAAyE;EAAzE;AAAyE;AAAzE;EAAA,kBAAyE;EAAzE,4DAAyE;EAAzE,oBAAyE;EAAzE;AAAyE;AAG3E,6BAA6B;AAa7B,mBAAmB;AAEjB;;EAAA;IAAA;EAA8E;AAAA;AAA9E;EAAA,kCAA8E;EAA9E,qBAA8E;EAA9E,iBAA8E;EAA9E,4DAA8E;EAA9E,sBAA8E;EAA9E;AAA8E;AAGhF,wBAAwB;AAEtB;EAAA,mBAA+C;EAA/C,qBAA+C;EAA/C,sBAA+C;EAA/C,aAA+C;EAA/C,+EAA+C;EAA/C,mGAA+C;EAA/C;AAA+C;AAmBjD,qBAAqB;AAKrB,0BAA0B;AA5L5B;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,SAAmB;EAAnB;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,wBAAmB;OAAnB;AAAmB;AAAnB;EAAA,uBAAmB;OAAnB;AAAmB;AAAnB;EAAA,qBAAmB;OAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,sFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAiNjB,4BAA4B;AAC5B;IACE,kBAAkB;IAClB,UAAU;IACV,WAAW;IACX,UAAU;IACV,YAAY;IACZ,gBAAgB;IAChB,sBAAsB;IACtB,mBAAmB;IACnB,SAAS;EACX;AAEA,iBAAiB;AACjB;IACE;MACE,wBAAwB;IAC1B;;IAEA;MACE,yBAAyB;IAC3B;;IAEA;MACE,eAAe;MACf,gBAAgB;IAClB;;IAEA;MACE,gBAAgB;MAChB,sBAAsB;IACxB;EACF;AAEA,iCAAiC;AAYjC,wBAAwB;AAaxB,kDAAkD;;AA1QpD,wBAAwB;AACxB,mGAAmG;;AAEnG,4CAA4C;;AAoC5C,qBAAqB;;AAqKrB,mBAAmB;;AA4EnB,mCAAmC;AACnC;EACE,WAAW;AACb;;AAGE;EAAA,kBAAkB;EAAlB;AAAkB;;AAIlB;EAAA,qBAA6B;EAA7B,kBAA6B;EAA7B;AAA6B;;AAI7B;EAAA,kBAAkB;EAAlB;AAAkB;;AAGpB,qBAAqB;AAEnB;EAAA,kBAAsC;EAAtC,4DAAsC;EAAtC,oBAAsC;EAAtC;AAAsC;AAAtC;EAAA,kBAAsC;EAAtC,4DAAsC;EAAtC,oBAAsC;EAAtC;AAAsC;;AAGxC,iDAAiD;AAE/C;EAAA,8BAAyD;EAAzD,mBAAyD;EAAzD,2GAAyD;EAAzD,yGAAyD;EAAzD,4FAAyD;EAAzD,oBAAyD;EAAzD,4DAAyD;EAAzD;AAAyD;AApT3D;EAAA,0BAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD,sBAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD,kBAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD,wBAqTC;EArTD,wDAqTC;EArTD;AAqTC;AArTD;EAAA,gBAqTC;EArTD;AAqTC;AArTD;EAAA,kBAqTC;EArTD;AAqTC;AArTD;EAAA,kBAqTC;EArTD;AAqTC;AArTD;EAAA,kBAqTC;EArTD;AAqTC;AArTD;EAAA,oBAqTC;EArTD;AAqTC;AArTD;EAAA,oBAqTC;EArTD;AAqTC;AArTD;EAAA,oBAqTC;EArTD;AAqTC;AArTD;EAAA,oBAqTC;EArTD;AAqTC;AArTD;EAAA,kFAqTC;EArTD,iGAqTC;EArTD;AAqTC;AArTD;EAAA,gBAqTC;EArTD,WAqTC;EArTD,YAqTC;EArTD,UAqTC;EArTD,SAqTC;EArTD,iBAqTC;EArTD,UAqTC;EArTD;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA,8BAqTC;EArTD;AAqTC;AArTD;EAAA,2GAqTC;EArTD,yGAqTC;EArTD;AAqTC;AArTD;EAAA,oBAqTC;EArTD;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA;AAqTC;AArTD;EAAA,kBAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD,sBAqTC;EArTD;AAqTC;AArTD;EAAA,0BAqTC;EArTD,sBAqTC;EArTD;AAqTC;AArTD;EAAA,8BAqTC;EArTD;AAqTC;AArTD;EAAA,2GAqTC;EArTD,yGAqTC;EArTD;AAqTC;AArTD;EAAA,oBAqTC;EArTD;AAqTC;AArTD;;EAAA;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA,iBAqTC;IArTD;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA,oBAqTC;IArTD;EAqTC;;EArTD;IAAA,oBAqTC;IArTD;EAqTC;;EArTD;IAAA,iBAqTC;IArTD;EAqTC;;EArTD;IAAA,kBAqTC;IArTD;EAqTC;;EArTD;IAAA,kBAqTC;IArTD;EAqTC;;EArTD;IAAA,kBAqTC;IArTD;EAqTC;AAAA;AArTD;;EAAA;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;AAAA;AArTD;;EAAA;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA;EAqTC;;EArTD;IAAA,uBAqTC;IArTD,uDAqTC;IArTD;EAqTC;;EArTD;IAAA,kBAqTC;IArTD;EAqTC;;EArTD;IAAA,kBAqTC;IArTD;EAqTC;;EArTD;IAAA;EAqTC;AAAA\",\"sourcesContent\":[\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* Import Google Fonts */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\\n\\n/* Base styles for elderly-friendly design */\\n@layer base {\\n  html {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\n\\n  body {\\n    @apply bg-elderly-bg text-elderly-text antialiased;\\n    font-size: 18px;\\n    /* Larger base font size for elderly users */\\n    line-height: 1.6;\\n  }\\n\\n  /* Focus styles for accessibility */\\n  *:focus {\\n    @apply outline-none ring-2 ring-primary-500 ring-offset-2;\\n  }\\n\\n  /* High contrast mode support */\\n  @media (prefers-contrast: high) {\\n    body {\\n      @apply bg-white text-black;\\n    }\\n  }\\n\\n  /* Reduced motion support */\\n  @media (prefers-reduced-motion: reduce) {\\n    * {\\n      animation-duration: 0.01ms !important;\\n      animation-iteration-count: 1 !important;\\n      transition-duration: 0.01ms !important;\\n    }\\n  }\\n}\\n\\n/* Component styles */\\n@layer components {\\n\\n  /* Button styles */\\n  .btn {\\n    @apply btn-elderly inline-flex items-center justify-center border border-transparent font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;\\n  }\\n\\n  .btn-primary {\\n    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;\\n  }\\n\\n  .btn-secondary {\\n    @apply bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500;\\n  }\\n\\n  .btn-success {\\n    @apply bg-health-normal text-white hover:bg-green-600 focus:ring-green-500;\\n  }\\n\\n  .btn-warning {\\n    @apply bg-health-warning text-white hover:bg-yellow-600 focus:ring-yellow-500;\\n  }\\n\\n  .btn-danger {\\n    @apply bg-health-danger text-white hover:bg-red-600 focus:ring-red-500;\\n  }\\n\\n  .btn-outline {\\n    @apply bg-transparent border-2 border-current hover:bg-current hover:text-white;\\n  }\\n\\n  .btn-lg {\\n    @apply px-8 py-4 text-xl;\\n  }\\n\\n  .btn-sm {\\n    @apply px-4 py-2 text-base;\\n  }\\n\\n  /* Card styles */\\n  .card {\\n    @apply card-elderly transition-shadow duration-200;\\n  }\\n\\n  .card:hover {\\n    @apply shadow-card-hover;\\n  }\\n\\n  .card-header {\\n    @apply border-b border-elderly-border pb-4 mb-4;\\n  }\\n\\n  .card-title {\\n    @apply text-xl font-semibold text-elderly-text;\\n  }\\n\\n  .card-subtitle {\\n    @apply text-elderly-light mt-1;\\n  }\\n\\n  /* Form styles */\\n  .form-group {\\n    @apply mb-6;\\n  }\\n\\n  .form-label {\\n    @apply block text-elderly-text font-medium mb-2;\\n  }\\n\\n  .form-input {\\n    @apply input-elderly w-full;\\n  }\\n\\n  .form-select {\\n    @apply input-elderly w-full;\\n  }\\n\\n  .form-textarea {\\n    @apply input-elderly w-full resize-y;\\n  }\\n\\n  .form-error {\\n    @apply text-health-danger text-sm mt-1;\\n  }\\n\\n  .form-help {\\n    @apply text-elderly-light text-sm mt-1;\\n  }\\n\\n  /* Navigation styles */\\n  .nav-link {\\n    @apply px-4 py-3 text-lg font-medium rounded-lg transition-colors duration-200;\\n  }\\n\\n  .nav-link-active {\\n    @apply bg-primary-100 text-primary-700;\\n  }\\n\\n  .nav-link-inactive {\\n    @apply text-elderly-text hover:bg-elderly-hover-bg hover:text-primary-600;\\n  }\\n\\n  /* Health status indicators */\\n  .health-status-normal {\\n    @apply bg-green-100 text-green-800 border border-green-200;\\n  }\\n\\n  .health-status-warning {\\n    @apply bg-yellow-100 text-yellow-800 border border-yellow-200;\\n  }\\n\\n  .health-status-danger {\\n    @apply bg-red-100 text-red-800 border border-red-200;\\n  }\\n\\n  /* Loading states */\\n  .loading-spinner {\\n    @apply animate-spin rounded-full border-4 border-gray-200 border-t-primary-600;\\n  }\\n\\n  /* Toast notifications */\\n  .toast {\\n    @apply rounded-lg shadow-lg p-4 mb-4 border-l-4;\\n  }\\n\\n  .toast-success {\\n    @apply bg-green-50 border-green-400 text-green-800;\\n  }\\n\\n  .toast-error {\\n    @apply bg-red-50 border-red-400 text-red-800;\\n  }\\n\\n  .toast-warning {\\n    @apply bg-yellow-50 border-yellow-400 text-yellow-800;\\n  }\\n\\n  .toast-info {\\n    @apply bg-blue-50 border-blue-400 text-blue-800;\\n  }\\n\\n  /* Chart containers */\\n  .chart-container {\\n    @apply relative w-full h-64 md:h-80 lg:h-96;\\n  }\\n\\n  /* Responsive text sizes */\\n  .text-responsive-sm {\\n    @apply text-base md:text-lg;\\n  }\\n\\n  .text-responsive-md {\\n    @apply text-lg md:text-xl;\\n  }\\n\\n  .text-responsive-lg {\\n    @apply text-xl md:text-2xl;\\n  }\\n\\n  .text-responsive-xl {\\n    @apply text-2xl md:text-3xl;\\n  }\\n}\\n\\n/* Utility styles */\\n@layer utilities {\\n\\n  /* Accessibility utilities */\\n  .sr-only {\\n    position: absolute;\\n    width: 1px;\\n    height: 1px;\\n    padding: 0;\\n    margin: -1px;\\n    overflow: hidden;\\n    clip: rect(0, 0, 0, 0);\\n    white-space: nowrap;\\n    border: 0;\\n  }\\n\\n  /* Print styles */\\n  @media print {\\n    .no-print {\\n      display: none !important;\\n    }\\n\\n    .print-only {\\n      display: block !important;\\n    }\\n\\n    body {\\n      font-size: 12pt;\\n      line-height: 1.4;\\n    }\\n\\n    .card {\\n      box-shadow: none;\\n      border: 1px solid #ccc;\\n    }\\n  }\\n\\n  /* High contrast mode utilities */\\n  @media (prefers-contrast: high) {\\n    .high-contrast-border {\\n      border: 2px solid currentColor !important;\\n    }\\n\\n    .high-contrast-bg {\\n      background-color: white !important;\\n      color: black !important;\\n    }\\n  }\\n\\n  /* Animation utilities */\\n  .animate-fade-in {\\n    animation: fadeIn 0.5s ease-in-out;\\n  }\\n\\n  .animate-slide-up {\\n    animation: slideUp 0.3s ease-out;\\n  }\\n\\n  .animate-slide-down {\\n    animation: slideDown 0.3s ease-out;\\n  }\\n\\n  /* Spacing utilities for elderly-friendly design */\\n  .space-elderly>*+* {\\n    margin-top: 1.5rem;\\n  }\\n\\n  .space-elderly-sm>*+* {\\n    margin-top: 1rem;\\n  }\\n\\n  .space-elderly-lg>*+* {\\n    margin-top: 2rem;\\n  }\\n}\\n\\n/* Custom scrollbar for better UX */\\n::-webkit-scrollbar {\\n  width: 12px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  @apply bg-gray-100;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  @apply bg-gray-400 rounded-lg;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  @apply bg-gray-500;\\n}\\n\\n/* Selection styles */\\n::selection {\\n  @apply bg-primary-200 text-primary-900;\\n}\\n\\n/* Focus visible for better keyboard navigation */\\n.focus-visible:focus {\\n  @apply outline-none ring-2 ring-primary-500 ring-offset-2;\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});