"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiUtils: function() { return /* binding */ apiUtils; },\n/* harmony export */   chatApi: function() { return /* binding */ chatApi; },\n/* harmony export */   dashboardApi: function() { return /* binding */ dashboardApi; },\n/* harmony export */   healthApi: function() { return /* binding */ healthApi; },\n/* harmony export */   medicationApi: function() { return /* binding */ medicationApi; },\n/* harmony export */   scheduleApi: function() { return /* binding */ scheduleApi; },\n/* harmony export */   userApi: function() { return /* binding */ userApi; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/**\n * API client for Elderly Health Support System\n */ \n// API configuration\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 10000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use(async (config)=>{\n        try {\n            // Get token from localStorage (simple auth)\n            if (true) {\n                const token = localStorage.getItem(\"auth_token\");\n                if (token) {\n                    config.headers.Authorization = \"Bearer \".concat(token);\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to get auth token:\", error);\n        }\n        return config;\n    }, (error)=>{\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>response, (error)=>{\n        var _error_response;\n        if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n            // Redirect to login on unauthorized\n            if (true) {\n                localStorage.removeItem(\"auth_token\");\n                window.location.href = \"/auth/login\";\n            }\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\n// API client instance\nconst apiClient = createApiClient();\n// Generic API request function\nconst apiRequest = async (method, url, data, config)=>{\n    try {\n        const response = await apiClient.request({\n            method,\n            url,\n            data,\n            ...config\n        });\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"API \".concat(method, \" \").concat(url, \" error:\"), error);\n        throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"An unexpected error occurred\");\n    }\n};\n// User API\nconst userApi = {\n    // Get current user profile\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/users/me\"),\n    // Create user profile\n    createUser: (userData)=>apiRequest(\"POST\", \"/users\", userData),\n    // Update user profile\n    updateUser: (userData)=>apiRequest(\"PUT\", \"/users/me\", userData),\n    // Get health profile\n    getHealthProfile: ()=>apiRequest(\"GET\", \"/users/me/health-profile\"),\n    // Create health profile\n    createHealthProfile: (profileData)=>apiRequest(\"POST\", \"/users/me/health-profile\", profileData),\n    // Update health profile\n    updateHealthProfile: (profileData)=>apiRequest(\"PUT\", \"/users/me/health-profile\", profileData),\n    // Get user settings\n    getSettings: ()=>apiRequest(\"GET\", \"/users/me/settings\"),\n    // Create/update user setting\n    updateSetting: (key, value)=>apiRequest(\"POST\", \"/users/me/settings\", {\n            setting_key: key,\n            setting_value: value\n        })\n};\n// Health Records API\nconst healthApi = {\n    // Get health records\n    getRecords: (params)=>apiRequest(\"GET\", \"/health/records\", undefined, {\n            params\n        }),\n    // Create health record\n    createRecord: (recordData)=>apiRequest(\"POST\", \"/health/records\", recordData),\n    // Get specific health record\n    getRecord: (recordId)=>apiRequest(\"GET\", \"/health/records/\".concat(recordId)),\n    // Delete health record\n    deleteRecord: (recordId)=>apiRequest(\"DELETE\", \"/health/records/\".concat(recordId)),\n    // Get health statistics\n    getStats: (recordType)=>apiRequest(\"GET\", \"/health/stats\", undefined, {\n            params: recordType ? {\n                record_type: recordType\n            } : undefined\n        })\n};\n// Medications API\nconst medicationApi = {\n    // Get medications\n    getMedications: function() {\n        let activeOnly = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return apiRequest(\"GET\", \"/medications\", undefined, {\n            params: {\n                active_only: activeOnly\n            }\n        });\n    },\n    // Create medication\n    createMedication: (medicationData)=>apiRequest(\"POST\", \"/medications\", medicationData),\n    // Get specific medication\n    getMedication: (medicationId)=>apiRequest(\"GET\", \"/medications/\".concat(medicationId)),\n    // Update medication\n    updateMedication: (medicationId, medicationData)=>apiRequest(\"PUT\", \"/medications/\".concat(medicationId), medicationData),\n    // Delete medication\n    deleteMedication: (medicationId)=>apiRequest(\"DELETE\", \"/medications/\".concat(medicationId))\n};\n// Schedules API\nconst scheduleApi = {\n    // Get schedules\n    getSchedules: (params)=>apiRequest(\"GET\", \"/schedules\", undefined, {\n            params\n        }),\n    // Create schedule\n    createSchedule: (scheduleData)=>apiRequest(\"POST\", \"/schedules\", scheduleData),\n    // Get today's schedules\n    getTodaySchedules: ()=>apiRequest(\"GET\", \"/schedules/today\"),\n    // Get specific schedule\n    getSchedule: (scheduleId)=>apiRequest(\"GET\", \"/schedules/\".concat(scheduleId)),\n    // Update schedule\n    updateSchedule: (scheduleId, scheduleData)=>apiRequest(\"PUT\", \"/schedules/\".concat(scheduleId), scheduleData),\n    // Delete schedule\n    deleteSchedule: (scheduleId)=>apiRequest(\"DELETE\", \"/schedules/\".concat(scheduleId)),\n    // Get reminders\n    getReminders: (params)=>apiRequest(\"GET\", \"/schedules/reminders\", undefined, {\n            params\n        }),\n    // Mark reminder as read\n    markReminderRead: (reminderId)=>apiRequest(\"PUT\", \"/schedules/reminders/\".concat(reminderId, \"/read\"))\n};\n// Chat API\nconst chatApi = {\n    // Create chat session\n    createSession: ()=>apiRequest(\"POST\", \"/chat/sessions\"),\n    // Get active session\n    getActiveSession: ()=>apiRequest(\"GET\", \"/chat/sessions/active\"),\n    // Send message\n    sendMessage: (sessionId, content)=>apiRequest(\"POST\", \"/chat/sessions/\".concat(sessionId, \"/messages\"), {\n            content\n        }),\n    // Get chat history\n    getChatHistory: (sessionId)=>apiRequest(\"GET\", \"/chat/sessions/\".concat(sessionId, \"/messages\")),\n    // End session\n    endSession: (sessionId)=>apiRequest(\"PUT\", \"/chat/sessions/\".concat(sessionId, \"/end\"))\n};\n// Dashboard API\nconst dashboardApi = {\n    // Get dashboard stats\n    getStats: ()=>apiRequest(\"GET\", \"/dashboard/stats\"),\n    // Get recent activity\n    getRecentActivity: function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return apiRequest(\"GET\", \"/dashboard/activity\", undefined, {\n            params: {\n                limit\n            }\n        });\n    },\n    // Get health summary\n    getHealthSummary: ()=>apiRequest(\"GET\", \"/dashboard/health-summary\")\n};\n// Utility functions\nconst apiUtils = {\n    // Check API health\n    checkHealth: ()=>apiRequest(\"GET\", \"/health\"),\n    // Get API info\n    getInfo: ()=>apiRequest(\"GET\", \"/info\"),\n    // Upload file (if needed)\n    uploadFile: async (file, endpoint)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return apiRequest(\"POST\", endpoint, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n    }\n};\n// Export default API client\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api.ts\n"));

/***/ })

});