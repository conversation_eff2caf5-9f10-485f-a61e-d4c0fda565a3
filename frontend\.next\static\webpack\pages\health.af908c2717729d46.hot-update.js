"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/health",{

/***/ "./pages/health/index.tsx":
/*!********************************!*\
  !*** ./pages/health/index.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,PlusIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,PlusIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst HealthPage = ()=>{\n    _s();\n    const [records, setRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const healthTypes = [\n        {\n            type: \"blood_pressure\",\n            name: \"Huyết \\xe1p\",\n            unit: \"mmHg\",\n            description: \"Ghi nhận chỉ số huyết \\xe1p h\\xe0ng ng\\xe0y\",\n            color: \"bg-red-50 border-red-200 text-red-800\",\n            icon: \"❤️\"\n        },\n        {\n            type: \"blood_sugar\",\n            name: \"Đường huyết\",\n            unit: \"mg/dL\",\n            description: \"Theo d\\xf5i mức đường huyết\",\n            color: \"bg-blue-50 border-blue-200 text-blue-800\",\n            icon: \"\\uD83E\\uDE78\"\n        },\n        {\n            type: \"weight\",\n            name: \"C\\xe2n nặng\",\n            unit: \"kg\",\n            description: \"Theo d\\xf5i c\\xe2n nặng\",\n            color: \"bg-green-50 border-green-200 text-green-800\",\n            icon: \"⚖️\"\n        },\n        {\n            type: \"heart_rate\",\n            name: \"Nhịp tim\",\n            unit: \"bpm\",\n            description: \"Theo d\\xf5i nhịp tim\",\n            color: \"bg-purple-50 border-purple-200 text-purple-800\",\n            icon: \"\\uD83D\\uDC93\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadHealthData();\n    }, []);\n    const loadHealthData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Load recent records\n            const recentRecords = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.healthApi.getRecords({\n                limit: 20\n            });\n            setRecords(recentRecords);\n            // Load stats for each health type\n            const statsPromises = healthTypes.map(async (type)=>{\n                try {\n                    return await _lib_api__WEBPACK_IMPORTED_MODULE_4__.healthApi.getStats(type.type);\n                } catch (e) {\n                    return {\n                        record_type: type.type,\n                        total_records: 0,\n                        latest_value: null,\n                        latest_date: null,\n                        average_last_7_days: null,\n                        trend: \"stable\"\n                    };\n                }\n            });\n            const statsResults = await Promise.all(statsPromises);\n            setStats(statsResults);\n        } catch (err) {\n            console.error(\"Error loading health data:\", err);\n            setError(\"Kh\\xf4ng thể tải dữ liệu sức khỏe\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddRecord = (type)=>{\n        setSelectedType(type);\n        setShowAddForm(true);\n    };\n    const getTypeConfig = (type)=>{\n        return healthTypes.find((t)=>t.type === type) || healthTypes[0];\n    };\n    const getTypeStats = (type)=>{\n        return stats.find((s)=>s.record_type === type);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"Theo d\\xf5i sức khỏe\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Theo d\\xf5i sức khỏe\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-elderly-text\",\n                            children: \"Theo d\\xf5i sức khỏe\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"btn btn-primary flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PlusIcon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ghi nhận mới\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadHealthData,\n                            className: \"mt-2 text-red-600 hover:text-red-800 text-sm underline\",\n                            children: \"Thử lại\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: healthTypes.map((type)=>{\n                        const typeStats = getTypeStats(type.type);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: type.icon\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: type.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon, {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light text-sm mb-4\",\n                                    children: type.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, undefined),\n                                typeStats && typeStats.latest_value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-elderly-text\",\n                                            children: typeStats.latest_value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-elderly-text-light\",\n                                            children: typeStats.latest_date ? new Date(typeStats.latest_date).toLocaleDateString(\"vi-VN\") : \"Chưa c\\xf3 dữ liệu\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-elderly-text-light\",\n                                            children: [\n                                                \"Tổng: \",\n                                                typeStats.total_records,\n                                                \" lần ghi nhận\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: \"Chưa c\\xf3 dữ liệu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleAddRecord(type.type),\n                                    className: \"btn btn-primary w-full\",\n                                    children: \"Ghi nhận\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, type.type, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Ghi nhận gần đ\\xe2y\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined),\n                        records.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: records.slice(0, 10).map((record)=>{\n                                const typeConfig = getTypeConfig(record.record_type);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-lg \".concat(typeConfig.color),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            typeConfig.icon,\n                                                            \" \",\n                                                            typeConfig.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-bold\",\n                                                        children: record.display_value\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: new Date(record.recorded_at).toLocaleDateString(\"vi-VN\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs\",\n                                                        children: new Date(record.recorded_at).toLocaleTimeString(\"vi-VN\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block px-2 py-1 rounded-full text-xs \".concat(record.is_normal ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                        children: record.is_normal ? \"B\\xecnh thường\" : \"Cần ch\\xfa \\xfd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, record.id, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Chưa c\\xf3 ghi nhận n\\xe0o\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddForm(true),\n                                    className: \"mt-4 btn btn-primary\",\n                                    children: \"Th\\xeam ghi nhận đầu ti\\xean\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HealthPage, \"zolw/uRPK8REVhDJw+vLVnCg1Ok=\");\n_c = HealthPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(HealthPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"HealthPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/health/index.tsx\n"));

/***/ })

});