"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./lib/auth.tsx":
/*!**********************!*\
  !*** ./lib/auth.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useUser: function() { return /* binding */ useUser; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/**\n * Simple authentication context and hooks\n */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            // Try to get token from cookie first, then localStorage\n            let savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"auth_token\");\n            if (!savedToken && \"object\" !== \"undefined\") {\n                savedToken = localStorage.getItem(\"auth_token\");\n            }\n            if (savedToken) {\n                setToken(savedToken);\n                try {\n                    // Verify token and get user info\n                    const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(API_BASE_URL, \"/auth/me\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(savedToken)\n                        }\n                    });\n                    setUser(response.data);\n                    // Ensure token is saved in both places\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", savedToken, {\n                        expires: 7\n                    }); // 7 days\n                    if (true) {\n                        localStorage.setItem(\"auth_token\", savedToken);\n                    }\n                } catch (error) {\n                    console.error(\"Token verification failed:\", error);\n                    // Remove invalid token from both places\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n                    if (true) {\n                        localStorage.removeItem(\"auth_token\");\n                    }\n                    setToken(null);\n                }\n            }\n            setIsLoading(false);\n        };\n        initAuth();\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"\".concat(API_BASE_URL, \"/auth/login\"), {\n                email,\n                password\n            });\n            const { access_token, user: userData } = response.data;\n            // Save token to both cookie and localStorage\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", access_token, {\n                expires: 7\n            }); // 7 days\n            if (true) {\n                localStorage.setItem(\"auth_token\", access_token);\n            }\n            setToken(access_token);\n            setUser(userData);\n            // Redirect to dashboard\n            router.push(\"/\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Login failed:\", error);\n            throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Login failed. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (email, password, full_name, phone)=>{\n        try {\n            setIsLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"\".concat(API_BASE_URL, \"/auth/register\"), {\n                email,\n                password,\n                full_name,\n                phone\n            });\n            const { access_token, user: userData } = response.data;\n            // Save token to both cookie and localStorage\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", access_token, {\n                expires: 7\n            }); // 7 days\n            if (true) {\n                localStorage.setItem(\"auth_token\", access_token);\n            }\n            setToken(access_token);\n            setUser(userData);\n            // Redirect to dashboard\n            router.push(\"/\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Registration failed:\", error);\n            throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Registration failed. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        // Remove token from cookie\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n        setToken(null);\n        setUser(null);\n        // Redirect to home\n        router.push(\"/\");\n    };\n    const value = {\n        user,\n        isLoading,\n        login,\n        register,\n        logout,\n        token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n        lineNumber: 179,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AuthProvider, \"D92vKOVhjWcaChOYbuVhF88zu6M=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// HOC for protected pages\nconst withAuth = (Component)=>{\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { user, isLoading } = useAuth();\n        const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!isLoading && !user) {\n                router.push(\"/auth/login\");\n            }\n        }, [\n            user,\n            isLoading,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            return null; // Will redirect\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n            lineNumber: 216,\n            columnNumber: 12\n        }, this);\n    }, \"FG2DkPvrCUydgYqHSyFl4Q88edA=\", false, function() {\n        return [\n            useAuth,\n            next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n        ];\n    });\n};\n// Hook for checking if user is authenticated\nconst useUser = ()=>{\n    _s2();\n    const { user, isLoading } = useAuth();\n    return {\n        user,\n        isLoading,\n        error: null\n    };\n};\n_s2(useUser, \"6lKHjqCqGIRsHh92bje8H78laow=\", false, function() {\n    return [\n        useAuth\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/auth.tsx\n"));

/***/ })

});