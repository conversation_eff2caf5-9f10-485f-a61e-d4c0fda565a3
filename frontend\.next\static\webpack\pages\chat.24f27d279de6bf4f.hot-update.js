"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiUtils: function() { return /* binding */ apiUtils; },\n/* harmony export */   authApi: function() { return /* binding */ authApi; },\n/* harmony export */   chatApi: function() { return /* binding */ chatApi; },\n/* harmony export */   dashboardApi: function() { return /* binding */ dashboardApi; },\n/* harmony export */   healthApi: function() { return /* binding */ healthApi; },\n/* harmony export */   medicationApi: function() { return /* binding */ medicationApi; },\n/* harmony export */   scheduleApi: function() { return /* binding */ scheduleApi; },\n/* harmony export */   userApi: function() { return /* binding */ userApi; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/**\n * API client for Elderly Health Support System\n */ \n\n// API configuration\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 10000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use(async (config)=>{\n        try {\n            // Get token from cookies (simple auth)\n            if (true) {\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"auth_token\");\n                if (token) {\n                    config.headers.Authorization = \"Bearer \".concat(token);\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to get auth token:\", error);\n        }\n        return config;\n    }, (error)=>{\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>response, (error)=>{\n        var _error_response;\n        if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n            // Redirect to login on unauthorized\n            if (true) {\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"auth_token\");\n                window.location.href = \"/auth/login\";\n            }\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\n// API client instance\nconst apiClient = createApiClient();\n// Generic API request function\nconst apiRequest = async (method, url, data, config)=>{\n    try {\n        const response = await apiClient.request({\n            method,\n            url,\n            data,\n            ...config\n        });\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"API \".concat(method, \" \").concat(url, \" error:\"), error);\n        throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"An unexpected error occurred\");\n    }\n};\n// Auth API\nconst authApi = {\n    // Login\n    login: async (email, password)=>{\n        const response = await apiRequest(\"POST\", \"/auth/login\", {\n            email,\n            password\n        });\n        // Store token in cookies\n        if ( true && response.token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"auth_token\", response.token, {\n                expires: 1\n            });\n        }\n        return response;\n    },\n    // Register\n    register: async (userData)=>{\n        const response = await apiRequest(\"POST\", \"/auth/register\", userData);\n        // Store token in cookies\n        if ( true && response.token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"auth_token\", response.token, {\n                expires: 1\n            });\n        }\n        return response;\n    },\n    // Logout\n    logout: ()=>{\n        if (true) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"auth_token\");\n            window.location.href = \"/auth/login\";\n        }\n    },\n    // Check if user is authenticated\n    isAuthenticated: ()=>{\n        if (true) {\n            return !!js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"auth_token\");\n        }\n        return false;\n    }\n};\n// User API\nconst userApi = {\n    // Get current user profile\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/users/me\"),\n    // Create user profile\n    createUser: (userData)=>apiRequest(\"POST\", \"/users\", userData),\n    // Update user profile\n    updateUser: (userData)=>apiRequest(\"PUT\", \"/users/me\", userData),\n    // Get health profile\n    getHealthProfile: ()=>apiRequest(\"GET\", \"/users/me/health-profile\"),\n    // Create health profile\n    createHealthProfile: (profileData)=>apiRequest(\"POST\", \"/users/me/health-profile\", profileData),\n    // Update health profile\n    updateHealthProfile: (profileData)=>apiRequest(\"PUT\", \"/users/me/health-profile\", profileData),\n    // Get user settings\n    getSettings: ()=>apiRequest(\"GET\", \"/users/me/settings\"),\n    // Create/update user setting\n    updateSetting: (key, value)=>apiRequest(\"POST\", \"/users/me/settings\", {\n            setting_key: key,\n            setting_value: value\n        })\n};\n// Health Records API\nconst healthApi = {\n    // Get health records\n    getRecords: (params)=>apiRequest(\"GET\", \"/health/records\", undefined, {\n            params\n        }),\n    // Create health record\n    createRecord: (recordData)=>apiRequest(\"POST\", \"/health/records\", recordData),\n    // Get specific health record\n    getRecord: (recordId)=>apiRequest(\"GET\", \"/health/records/\".concat(recordId)),\n    // Delete health record\n    deleteRecord: (recordId)=>apiRequest(\"DELETE\", \"/health/records/\".concat(recordId)),\n    // Get health statistics\n    getStats: (recordType)=>apiRequest(\"GET\", \"/health/stats\", undefined, {\n            params: recordType ? {\n                record_type: recordType\n            } : undefined\n        })\n};\n// Medications API\nconst medicationApi = {\n    // Get medications\n    getMedications: function() {\n        let activeOnly = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return apiRequest(\"GET\", \"/medications\", undefined, {\n            params: {\n                active_only: activeOnly\n            }\n        });\n    },\n    // Create medication\n    createMedication: (medicationData)=>apiRequest(\"POST\", \"/medications\", medicationData),\n    // Get specific medication\n    getMedication: (medicationId)=>apiRequest(\"GET\", \"/medications/\".concat(medicationId)),\n    // Update medication\n    updateMedication: (medicationId, medicationData)=>apiRequest(\"PUT\", \"/medications/\".concat(medicationId), medicationData),\n    // Delete medication\n    deleteMedication: (medicationId)=>apiRequest(\"DELETE\", \"/medications/\".concat(medicationId))\n};\n// Schedules API\nconst scheduleApi = {\n    // Get schedules\n    getSchedules: (params)=>apiRequest(\"GET\", \"/schedules\", undefined, {\n            params\n        }),\n    // Create schedule\n    createSchedule: (scheduleData)=>apiRequest(\"POST\", \"/schedules\", scheduleData),\n    // Get today's schedules\n    getTodaySchedules: ()=>apiRequest(\"GET\", \"/schedules/today\"),\n    // Get specific schedule\n    getSchedule: (scheduleId)=>apiRequest(\"GET\", \"/schedules/\".concat(scheduleId)),\n    // Update schedule\n    updateSchedule: (scheduleId, scheduleData)=>apiRequest(\"PUT\", \"/schedules/\".concat(scheduleId), scheduleData),\n    // Delete schedule\n    deleteSchedule: (scheduleId)=>apiRequest(\"DELETE\", \"/schedules/\".concat(scheduleId)),\n    // Get reminders\n    getReminders: (params)=>apiRequest(\"GET\", \"/schedules/reminders\", undefined, {\n            params\n        }),\n    // Mark reminder as read\n    markReminderRead: (reminderId)=>apiRequest(\"PUT\", \"/schedules/reminders/\".concat(reminderId, \"/read\"))\n};\n// Chat API\nconst chatApi = {\n    // Create chat session\n    createSession: ()=>apiRequest(\"POST\", \"/chat/sessions\"),\n    // Get active session\n    getActiveSession: ()=>apiRequest(\"GET\", \"/chat/sessions/active\"),\n    // Send message\n    sendMessage: (sessionId, content)=>apiRequest(\"POST\", \"/chat/sessions/\".concat(sessionId, \"/messages\"), {\n            content\n        }),\n    // Get chat history\n    getChatHistory: (sessionId)=>apiRequest(\"GET\", \"/chat/sessions/\".concat(sessionId, \"/messages\")),\n    // End session\n    endSession: (sessionId)=>apiRequest(\"PUT\", \"/chat/sessions/\".concat(sessionId, \"/end\"))\n};\n// Dashboard API\nconst dashboardApi = {\n    // Get dashboard stats\n    getStats: ()=>apiRequest(\"GET\", \"/dashboard/stats\"),\n    // Get recent activity\n    getRecentActivity: function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return apiRequest(\"GET\", \"/dashboard/activity\", undefined, {\n            params: {\n                limit\n            }\n        });\n    },\n    // Get health summary\n    getHealthSummary: ()=>apiRequest(\"GET\", \"/dashboard/health-summary\")\n};\n// Utility functions\nconst apiUtils = {\n    // Check API health\n    checkHealth: ()=>apiRequest(\"GET\", \"/health\"),\n    // Get API info\n    getInfo: ()=>apiRequest(\"GET\", \"/info\"),\n    // Upload file (if needed)\n    uploadFile: async (file, endpoint)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return apiRequest(\"POST\", endpoint, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n    }\n};\n// Export default API client\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api.ts\n"));

/***/ })

});