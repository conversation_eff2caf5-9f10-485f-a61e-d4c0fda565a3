"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./pages/chat/index.tsx":
/*!******************************!*\
  !*** ./pages/chat/index.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_PaperAirplaneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=PaperAirplaneIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=PaperAirplaneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst ChatPage = ()=>{\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            type: \"assistant\",\n            content: \"Xin ch\\xe0o! T\\xf4i l\\xe0 trợ l\\xfd AI sức khỏe. T\\xf4i c\\xf3 thể gi\\xfap bạn tư vấn về c\\xe1c vấn đề sức khỏe. Bạn cần hỗ trợ g\\xec h\\xf4m nay?\",\n            timestamp: new Date()\n        }\n    ]);\n    const handleSendMessage = ()=>{\n        if (!message.trim()) return;\n        // Add user message\n        const userMessage = {\n            id: messages.length + 1,\n            type: \"user\",\n            content: message,\n            timestamp: new Date()\n        };\n        // Simulate AI response\n        const aiResponse = {\n            id: messages.length + 2,\n            type: \"assistant\",\n            content: \"Cảm ơn bạn đ\\xe3 chia sẻ. Đ\\xe2y l\\xe0 phản hồi mẫu từ AI. Trong phi\\xean bản thực tế, t\\xf4i sẽ ph\\xe2n t\\xedch c\\xe2u hỏi của bạn v\\xe0 đưa ra lời tư vấn ph\\xf9 hợp.\",\n            timestamp: new Date()\n        };\n        setMessages([\n            ...messages,\n            userMessage,\n            aiResponse\n        ]);\n        setMessage(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Tư vấn AI\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-elderly-border\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-elderly-text\",\n                            children: \"Tư vấn AI sức khỏe\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-elderly-text-light mt-2\",\n                            children: \"Đặt c\\xe2u hỏi về sức khỏe v\\xe0 nhận tư vấn từ AI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n                    children: messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(msg.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg \".concat(msg.type === \"user\" ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-elderly-text\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: msg.content\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-1 \".concat(msg.type === \"user\" ? \"text-primary-100\" : \"text-elderly-text-light\"),\n                                        children: msg.timestamp.toLocaleTimeString(\"vi-VN\", {\n                                            hour: \"2-digit\",\n                                            minute: \"2-digit\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, undefined)\n                        }, msg.id, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-elderly-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: message,\n                                onChange: (e)=>setMessage(e.target.value),\n                                onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                placeholder: \"Nhập c\\xe2u hỏi của bạn...\",\n                                className: \"flex-1 form-input\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                className: \"btn btn-primary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PaperAirplaneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.PaperAirplaneIcon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatPage, \"3p/CIyESaL36YZ3/C5WroN4EZSA=\");\n_c = ChatPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(ChatPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9jaGF0L2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQW1EO0FBQ2I7QUFDVTtBQUNnQjtBQUloRSxNQUFNSyxXQUFxQjs7SUFDekIsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdOLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ08sVUFBVUMsWUFBWSxHQUFHUiwrQ0FBUUEsQ0FBQztRQUN2QztZQUNFUyxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsU0FDRTtZQUNGQyxXQUFXLElBQUlDO1FBQ2pCO0tBQ0Q7SUFFRCxNQUFNQyxvQkFBb0I7UUFDeEIsSUFBSSxDQUFDVCxRQUFRVSxJQUFJLElBQUk7UUFFckIsbUJBQW1CO1FBQ25CLE1BQU1DLGNBQWM7WUFDbEJQLElBQUlGLFNBQVNVLE1BQU0sR0FBRztZQUN0QlAsTUFBTTtZQUNOQyxTQUFTTjtZQUNUTyxXQUFXLElBQUlDO1FBQ2pCO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1LLGFBQWE7WUFDakJULElBQUlGLFNBQVNVLE1BQU0sR0FBRztZQUN0QlAsTUFBTTtZQUNOQyxTQUNFO1lBQ0ZDLFdBQVcsSUFBSUM7UUFDakI7UUFFQUwsWUFBWTtlQUFJRDtZQUFVUztZQUFhRTtTQUFXO1FBQ2xEWixXQUFXO0lBQ2I7SUFFQSxxQkFDRSw4REFBQ0osaUVBQU1BO1FBQUNpQixPQUFNO2tCQUNaLDRFQUFDQztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBdUM7Ozs7OztzQ0FHckQsOERBQUNFOzRCQUFFRixXQUFVO3NDQUErQjs7Ozs7Ozs7Ozs7OzhCQUs5Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1pkLFNBQVNpQixHQUFHLENBQUMsQ0FBQ0Msb0JBQ2IsOERBQUNMOzRCQUVDQyxXQUFXLFFBRVYsT0FEQ0ksSUFBSWYsSUFBSSxLQUFLLFNBQVMsZ0JBQWdCO3NDQUd4Qyw0RUFBQ1U7Z0NBQ0NDLFdBQVcsNkNBSVYsT0FIQ0ksSUFBSWYsSUFBSSxLQUFLLFNBQ1QsOEJBQ0E7O2tEQUdOLDhEQUFDYTtrREFBR0UsSUFBSWQsT0FBTzs7Ozs7O2tEQUNmLDhEQUFDWTt3Q0FDQ0YsV0FBVyxnQkFJVixPQUhDSSxJQUFJZixJQUFJLEtBQUssU0FDVCxxQkFDQTtrREFHTGUsSUFBSWIsU0FBUyxDQUFDYyxrQkFBa0IsQ0FBQyxTQUFTOzRDQUN6Q0MsTUFBTTs0Q0FDTkMsUUFBUTt3Q0FDVjs7Ozs7Ozs7Ozs7OzJCQXZCQ0gsSUFBSWhCLEVBQUU7Ozs7Ozs7Ozs7OEJBOEJqQiw4REFBQ1c7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ1E7Z0NBQ0NuQixNQUFLO2dDQUNMb0IsT0FBT3pCO2dDQUNQMEIsVUFBVSxDQUFDQyxJQUFNMUIsV0FBVzBCLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnQ0FDMUNJLFlBQVksQ0FBQ0YsSUFBTUEsRUFBRUcsR0FBRyxLQUFLLFdBQVdyQjtnQ0FDeENzQixhQUFZO2dDQUNaZixXQUFVOzs7Ozs7MENBRVosOERBQUNnQjtnQ0FBT0MsU0FBU3hCO2dDQUFtQk8sV0FBVTswQ0FDNUMsNEVBQUNsQixrSEFBaUJBO29DQUFDa0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzNDO0dBbkdNakI7S0FBQUE7QUFxR04sK0RBQWUsTUFBQUgsbURBQVFBLENBQUNHLFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcGFnZXMvY2hhdC9pbmRleC50c3g/N2RhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgd2l0aEF1dGggfSBmcm9tIFwiQC9saWIvYXV0aFwiO1xuaW1wb3J0IExheW91dCBmcm9tIFwiQC9jb21wb25lbnRzL0xheW91dC9MYXlvdXRcIjtcbmltcG9ydCB7IFBhcGVyQWlycGxhbmVJY29uIH0gZnJvbSBcIkBoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZVwiO1xuaW1wb3J0IHsgY2hhdEFwaSB9IGZyb20gXCJAL2xpYi9hcGlcIjtcbmltcG9ydCB7IENoYXRTZXNzaW9uLCBDaGF0TWVzc2FnZSB9IGZyb20gXCJAL3R5cGVzL2FwaVwiO1xuXG5jb25zdCBDaGF0UGFnZTogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIGNvbnN0IFttZXNzYWdlLCBzZXRNZXNzYWdlXSA9IHVzZVN0YXRlKFwiXCIpO1xuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlKFtcbiAgICB7XG4gICAgICBpZDogMSxcbiAgICAgIHR5cGU6IFwiYXNzaXN0YW50XCIsXG4gICAgICBjb250ZW50OlxuICAgICAgICBcIlhpbiBjaMOgbyEgVMO0aSBsw6AgdHLhu6MgbMO9IEFJIHPhu6ljIGto4buPZS4gVMO0aSBjw7MgdGjhu4MgZ2nDunAgYuG6oW4gdMawIHbhuqVuIHbhu4EgY8OhYyB24bqlbiDEkeG7gSBz4bupYyBraOG7j2UuIELhuqFuIGPhuqduIGjhu5cgdHLhu6MgZ8OsIGjDtG0gbmF5P1wiLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgIH0sXG4gIF0pO1xuXG4gIGNvbnN0IGhhbmRsZVNlbmRNZXNzYWdlID0gKCkgPT4ge1xuICAgIGlmICghbWVzc2FnZS50cmltKCkpIHJldHVybjtcblxuICAgIC8vIEFkZCB1c2VyIG1lc3NhZ2VcbiAgICBjb25zdCB1c2VyTWVzc2FnZSA9IHtcbiAgICAgIGlkOiBtZXNzYWdlcy5sZW5ndGggKyAxLFxuICAgICAgdHlwZTogXCJ1c2VyXCIsXG4gICAgICBjb250ZW50OiBtZXNzYWdlLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgIH07XG5cbiAgICAvLyBTaW11bGF0ZSBBSSByZXNwb25zZVxuICAgIGNvbnN0IGFpUmVzcG9uc2UgPSB7XG4gICAgICBpZDogbWVzc2FnZXMubGVuZ3RoICsgMixcbiAgICAgIHR5cGU6IFwiYXNzaXN0YW50XCIsXG4gICAgICBjb250ZW50OlxuICAgICAgICBcIkPhuqNtIMahbiBi4bqhbiDEkcOjIGNoaWEgc+G6uy4gxJDDonkgbMOgIHBo4bqjbiBo4buTaSBt4bqrdSB04burIEFJLiBUcm9uZyBwaGnDqm4gYuG6o24gdGjhu7FjIHThur8sIHTDtGkgc+G6vSBwaMOibiB0w61jaCBjw6J1IGjhu49pIGPhu6dhIGLhuqFuIHbDoCDEkcawYSByYSBs4budaSB0xrAgduG6pW4gcGjDuSBo4bujcC5cIixcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICB9O1xuXG4gICAgc2V0TWVzc2FnZXMoWy4uLm1lc3NhZ2VzLCB1c2VyTWVzc2FnZSwgYWlSZXNwb25zZV0pO1xuICAgIHNldE1lc3NhZ2UoXCJcIik7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8TGF5b3V0IHRpdGxlPVwiVMawIHbhuqVuIEFJXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1zY3JlZW5cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLWVsZGVybHktYm9yZGVyXCI+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWVsZGVybHktdGV4dFwiPlxuICAgICAgICAgICAgVMawIHbhuqVuIEFJIHPhu6ljIGto4buPZVxuICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1lbGRlcmx5LXRleHQtbGlnaHQgbXQtMlwiPlxuICAgICAgICAgICAgxJDhurd0IGPDonUgaOG7j2kgduG7gSBz4bupYyBraOG7j2UgdsOgIG5o4bqtbiB0xrAgduG6pW4gdOG7qyBBSVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIHAtNiBzcGFjZS15LTRcIj5cbiAgICAgICAgICB7bWVzc2FnZXMubWFwKChtc2cpID0+IChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAga2V5PXttc2cuaWR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggJHtcbiAgICAgICAgICAgICAgICBtc2cudHlwZSA9PT0gXCJ1c2VyXCIgPyBcImp1c3RpZnktZW5kXCIgOiBcImp1c3RpZnktc3RhcnRcIlxuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YG1heC13LXhzIGxnOm1heC13LW1kIHB4LTQgcHktMiByb3VuZGVkLWxnICR7XG4gICAgICAgICAgICAgICAgICBtc2cudHlwZSA9PT0gXCJ1c2VyXCJcbiAgICAgICAgICAgICAgICAgICAgPyBcImJnLXByaW1hcnktNjAwIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgICA6IFwiYmctZ3JheS0xMDAgdGV4dC1lbGRlcmx5LXRleHRcIlxuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHA+e21zZy5jb250ZW50fTwvcD5cbiAgICAgICAgICAgICAgICA8cFxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyBtdC0xICR7XG4gICAgICAgICAgICAgICAgICAgIG1zZy50eXBlID09PSBcInVzZXJcIlxuICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LXByaW1hcnktMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1lbGRlcmx5LXRleHQtbGlnaHRcIlxuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge21zZy50aW1lc3RhbXAudG9Mb2NhbGVUaW1lU3RyaW5nKFwidmktVk5cIiwge1xuICAgICAgICAgICAgICAgICAgICBob3VyOiBcIjItZGlnaXRcIixcbiAgICAgICAgICAgICAgICAgICAgbWludXRlOiBcIjItZGlnaXRcIixcbiAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLXQgYm9yZGVyLWVsZGVybHktYm9yZGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgdmFsdWU9e21lc3NhZ2V9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TWVzc2FnZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIG9uS2V5UHJlc3M9eyhlKSA9PiBlLmtleSA9PT0gXCJFbnRlclwiICYmIGhhbmRsZVNlbmRNZXNzYWdlKCl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTmjhuq1wIGPDonUgaOG7j2kgY+G7p2EgYuG6oW4uLi5cIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTZW5kTWVzc2FnZX0gY2xhc3NOYW1lPVwiYnRuIGJ0bi1wcmltYXJ5XCI+XG4gICAgICAgICAgICAgIDxQYXBlckFpcnBsYW5lSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvTGF5b3V0PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgd2l0aEF1dGgoQ2hhdFBhZ2UpO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ3aXRoQXV0aCIsIkxheW91dCIsIlBhcGVyQWlycGxhbmVJY29uIiwiQ2hhdFBhZ2UiLCJtZXNzYWdlIiwic2V0TWVzc2FnZSIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJpZCIsInR5cGUiLCJjb250ZW50IiwidGltZXN0YW1wIiwiRGF0ZSIsImhhbmRsZVNlbmRNZXNzYWdlIiwidHJpbSIsInVzZXJNZXNzYWdlIiwibGVuZ3RoIiwiYWlSZXNwb25zZSIsInRpdGxlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwibWFwIiwibXNnIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwiaG91ciIsIm1pbnV0ZSIsImlucHV0IiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvbktleVByZXNzIiwia2V5IiwicGxhY2Vob2xkZXIiLCJidXR0b24iLCJvbkNsaWNrIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/chat/index.tsx\n"));

/***/ })

});