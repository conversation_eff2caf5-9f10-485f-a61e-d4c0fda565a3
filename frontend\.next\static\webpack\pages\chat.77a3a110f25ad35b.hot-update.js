"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiUtils: function() { return /* binding */ apiUtils; },\n/* harmony export */   authApi: function() { return /* binding */ authApi; },\n/* harmony export */   chatApi: function() { return /* binding */ chatApi; },\n/* harmony export */   dashboardApi: function() { return /* binding */ dashboardApi; },\n/* harmony export */   healthApi: function() { return /* binding */ healthApi; },\n/* harmony export */   medicationApi: function() { return /* binding */ medicationApi; },\n/* harmony export */   scheduleApi: function() { return /* binding */ scheduleApi; },\n/* harmony export */   userApi: function() { return /* binding */ userApi; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/**\n * API client for Elderly Health Support System\n */ \n\n// API configuration\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 10000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use(async (config)=>{\n        try {\n            // Get token from cookies (simple auth)\n            if (true) {\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"auth_token\");\n                console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"\".concat(token.substring(0, 20), \"...\") : \"NO TOKEN\");\n                if (token) {\n                    config.headers.Authorization = \"Bearer \".concat(token);\n                    console.log(\"✅ Authorization header set\");\n                } else {\n                    console.warn(\"❌ No auth token found in cookies\");\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to get auth token:\", error);\n        }\n        return config;\n    }, (error)=>{\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>response, (error)=>{\n        var _error_response, _error_response1, _error_response2, _error_config, _error_config1, _error_config2, _error_response3;\n        // Log error for debugging\n        console.error(\"API Error:\", {\n            status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n            statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n            data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n            url: (_error_config = error.config) === null || _error_config === void 0 ? void 0 : _error_config.url,\n            method: (_error_config1 = error.config) === null || _error_config1 === void 0 ? void 0 : _error_config1.method,\n            headers: (_error_config2 = error.config) === null || _error_config2 === void 0 ? void 0 : _error_config2.headers\n        });\n        if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 401) {\n            // Redirect to login on unauthorized\n            console.warn(\"401 Unauthorized - redirecting to login\");\n        // TEMPORARILY DISABLED FOR DEBUGGING\n        // if (typeof window !== 'undefined') {\n        //   Cookies.remove('auth_token');\n        //   window.location.href = '/auth/login';\n        // }\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\n// API client instance\nconst apiClient = createApiClient();\n// Generic API request function\nconst apiRequest = async (method, url, data, config)=>{\n    try {\n        const response = await apiClient.request({\n            method,\n            url,\n            data,\n            ...config\n        });\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"API \".concat(method, \" \").concat(url, \" error:\"), error);\n        throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"An unexpected error occurred\");\n    }\n};\n// Auth API\nconst authApi = {\n    // Login\n    login: async (email, password)=>{\n        const response = await apiRequest(\"POST\", \"/auth/login\", {\n            email,\n            password\n        });\n        // Store token in cookies\n        if ( true && response.access_token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"auth_token\", response.access_token, {\n                expires: 1\n            });\n        }\n        return response;\n    },\n    // Register\n    register: async (userData)=>{\n        const response = await apiRequest(\"POST\", \"/auth/register\", userData);\n        // Store token in cookies\n        if ( true && response.access_token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"auth_token\", response.access_token, {\n                expires: 1\n            });\n        }\n        return response;\n    },\n    // Logout\n    logout: ()=>{\n        if (true) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"auth_token\");\n            window.location.href = \"/auth/login\";\n        }\n    },\n    // Check if user is authenticated\n    isAuthenticated: ()=>{\n        if (true) {\n            return !!js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"auth_token\");\n        }\n        return false;\n    }\n};\n// User API\nconst userApi = {\n    // Get current user profile\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/users/me\"),\n    // Create user profile\n    createUser: (userData)=>apiRequest(\"POST\", \"/users\", userData),\n    // Update user profile\n    updateUser: (userData)=>apiRequest(\"PUT\", \"/users/me\", userData),\n    // Get health profile\n    getHealthProfile: ()=>apiRequest(\"GET\", \"/users/me/health-profile\"),\n    // Create health profile\n    createHealthProfile: (profileData)=>apiRequest(\"POST\", \"/users/me/health-profile\", profileData),\n    // Update health profile\n    updateHealthProfile: (profileData)=>apiRequest(\"PUT\", \"/users/me/health-profile\", profileData),\n    // Get user settings\n    getSettings: ()=>apiRequest(\"GET\", \"/users/me/settings\"),\n    // Create/update user setting\n    updateSetting: (key, value)=>apiRequest(\"POST\", \"/users/me/settings\", {\n            setting_key: key,\n            setting_value: value\n        })\n};\n// Health Records API\nconst healthApi = {\n    // Get health records\n    getRecords: (params)=>apiRequest(\"GET\", \"/health/records\", undefined, {\n            params\n        }),\n    // Create health record\n    createRecord: (recordData)=>apiRequest(\"POST\", \"/health/records\", recordData),\n    // Get specific health record\n    getRecord: (recordId)=>apiRequest(\"GET\", \"/health/records/\".concat(recordId)),\n    // Delete health record\n    deleteRecord: (recordId)=>apiRequest(\"DELETE\", \"/health/records/\".concat(recordId)),\n    // Get health statistics\n    getStats: (recordType)=>apiRequest(\"GET\", \"/health/stats\", undefined, {\n            params: recordType ? {\n                record_type: recordType\n            } : undefined\n        })\n};\n// Medications API\nconst medicationApi = {\n    // Get medications\n    getMedications: function() {\n        let activeOnly = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return apiRequest(\"GET\", \"/medications\", undefined, {\n            params: {\n                active_only: activeOnly\n            }\n        });\n    },\n    // Create medication\n    createMedication: (medicationData)=>apiRequest(\"POST\", \"/medications\", medicationData),\n    // Get specific medication\n    getMedication: (medicationId)=>apiRequest(\"GET\", \"/medications/\".concat(medicationId)),\n    // Update medication\n    updateMedication: (medicationId, medicationData)=>apiRequest(\"PUT\", \"/medications/\".concat(medicationId), medicationData),\n    // Delete medication\n    deleteMedication: (medicationId)=>apiRequest(\"DELETE\", \"/medications/\".concat(medicationId))\n};\n// Schedules API\nconst scheduleApi = {\n    // Get schedules\n    getSchedules: (params)=>apiRequest(\"GET\", \"/schedules\", undefined, {\n            params\n        }),\n    // Create schedule\n    createSchedule: (scheduleData)=>apiRequest(\"POST\", \"/schedules\", scheduleData),\n    // Get today's schedules\n    getTodaySchedules: ()=>apiRequest(\"GET\", \"/schedules/today\"),\n    // Get specific schedule\n    getSchedule: (scheduleId)=>apiRequest(\"GET\", \"/schedules/\".concat(scheduleId)),\n    // Update schedule\n    updateSchedule: (scheduleId, scheduleData)=>apiRequest(\"PUT\", \"/schedules/\".concat(scheduleId), scheduleData),\n    // Delete schedule\n    deleteSchedule: (scheduleId)=>apiRequest(\"DELETE\", \"/schedules/\".concat(scheduleId)),\n    // Get reminders\n    getReminders: (params)=>apiRequest(\"GET\", \"/schedules/reminders\", undefined, {\n            params\n        }),\n    // Mark reminder as read\n    markReminderRead: (reminderId)=>apiRequest(\"PUT\", \"/schedules/reminders/\".concat(reminderId, \"/read\"))\n};\n// Chat API\nconst chatApi = {\n    // Create chat session\n    createSession: ()=>apiRequest(\"POST\", \"/chat/sessions\"),\n    // Get active session\n    getActiveSession: ()=>apiRequest(\"GET\", \"/chat/sessions/active\"),\n    // Send message\n    sendMessage: (sessionId, content)=>apiRequest(\"POST\", \"/chat/sessions/\".concat(sessionId, \"/messages\"), {\n            content\n        }),\n    // Get chat history\n    getChatHistory: (sessionId)=>apiRequest(\"GET\", \"/chat/sessions/\".concat(sessionId, \"/messages\")),\n    // End session\n    endSession: (sessionId)=>apiRequest(\"PUT\", \"/chat/sessions/\".concat(sessionId, \"/end\"))\n};\n// Dashboard API\nconst dashboardApi = {\n    // Get dashboard stats\n    getStats: ()=>apiRequest(\"GET\", \"/dashboard/stats\"),\n    // Get recent activity\n    getRecentActivity: function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return apiRequest(\"GET\", \"/dashboard/activity\", undefined, {\n            params: {\n                limit\n            }\n        });\n    },\n    // Get health summary\n    getHealthSummary: ()=>apiRequest(\"GET\", \"/dashboard/health-summary\")\n};\n// Utility functions\nconst apiUtils = {\n    // Check API health\n    checkHealth: ()=>apiRequest(\"GET\", \"/health\"),\n    // Get API info\n    getInfo: ()=>apiRequest(\"GET\", \"/info\"),\n    // Upload file (if needed)\n    uploadFile: async (file, endpoint)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return apiRequest(\"POST\", endpoint, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n    }\n};\n// Export default API client\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api.ts\n"));

/***/ })

});