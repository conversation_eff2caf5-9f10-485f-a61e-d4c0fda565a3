"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiUtils: function() { return /* binding */ apiUtils; },\n/* harmony export */   authApi: function() { return /* binding */ authApi; },\n/* harmony export */   chatApi: function() { return /* binding */ chatApi; },\n/* harmony export */   dashboardApi: function() { return /* binding */ dashboardApi; },\n/* harmony export */   healthApi: function() { return /* binding */ healthApi; },\n/* harmony export */   medicationsApi: function() { return /* binding */ medicationsApi; },\n/* harmony export */   schedulesApi: function() { return /* binding */ schedulesApi; },\n/* harmony export */   userApi: function() { return /* binding */ userApi; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/**\n * API client for Elderly Health Support System\n */ \n\n// API configuration\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 10000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use(async (config)=>{\n        try {\n            // Get token from cookies (simple auth)\n            if (true) {\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"auth_token\");\n                console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"\".concat(token.substring(0, 20), \"...\") : \"NO TOKEN\");\n                if (token) {\n                    // Decode token to check if it's valid\n                    try {\n                        const payload = JSON.parse(atob(token.split(\".\")[1]));\n                        console.log(\"\\uD83D\\uDD0D Token payload:\", payload);\n                        console.log(\"⏰ Token expires:\", new Date(payload.exp * 1000));\n                        console.log(\"\\uD83D\\uDD50 Current time:\", new Date());\n                        if (payload.exp * 1000 < Date.now()) {\n                            console.error(\"❌ Token has expired!\");\n                        } else {\n                            console.log(\"✅ Token is still valid\");\n                        }\n                    } catch (e) {\n                        console.error(\"❌ Failed to decode token:\", e);\n                    }\n                    config.headers.Authorization = \"Bearer \".concat(token);\n                    console.log(\"✅ Authorization header set\");\n                } else {\n                    console.warn(\"❌ No auth token found in cookies\");\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to get auth token:\", error);\n        }\n        return config;\n    }, (error)=>{\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>response, (error)=>{\n        var _error_response, _error_response1, _error_response2, _error_config, _error_config1, _error_config2, _error_response3;\n        // Log error for debugging\n        console.error(\"API Error:\", {\n            status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n            statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n            data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n            url: (_error_config = error.config) === null || _error_config === void 0 ? void 0 : _error_config.url,\n            method: (_error_config1 = error.config) === null || _error_config1 === void 0 ? void 0 : _error_config1.method,\n            headers: (_error_config2 = error.config) === null || _error_config2 === void 0 ? void 0 : _error_config2.headers\n        });\n        if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 401) {\n            // Redirect to login on unauthorized\n            console.warn(\"401 Unauthorized - redirecting to login\");\n        // TEMPORARILY DISABLED FOR DEBUGGING\n        // if (typeof window !== 'undefined') {\n        //   Cookies.remove('auth_token');\n        //   window.location.href = '/auth/login';\n        // }\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\n// API client instance\nconst apiClient = createApiClient();\n// Generic API request function\nconst apiRequest = async (method, url, data, config)=>{\n    try {\n        const response = await apiClient.request({\n            method,\n            url,\n            data,\n            ...config\n        });\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"API \".concat(method, \" \").concat(url, \" error:\"), error);\n        throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"An unexpected error occurred\");\n    }\n};\n// Auth API\nconst authApi = {\n    // Login\n    login: async (email, password)=>{\n        const response = await apiRequest(\"POST\", \"/auth/login\", {\n            email,\n            password\n        });\n        // Store token in cookies\n        if ( true && response.access_token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"auth_token\", response.access_token, {\n                expires: 1\n            });\n        }\n        return response;\n    },\n    // Register\n    register: async (userData)=>{\n        const response = await apiRequest(\"POST\", \"/auth/register\", userData);\n        // Store token in cookies\n        if ( true && response.access_token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"auth_token\", response.access_token, {\n                expires: 1\n            });\n        }\n        return response;\n    },\n    // Logout\n    logout: ()=>{\n        if (true) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"auth_token\");\n            window.location.href = \"/auth/login\";\n        }\n    },\n    // Check if user is authenticated\n    isAuthenticated: ()=>{\n        if (true) {\n            return !!js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"auth_token\");\n        }\n        return false;\n    },\n    // Get current user info\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/auth/me\")\n};\n// User API\nconst userApi = {\n    // Get current user profile\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/users/me\"),\n    // Create user profile\n    createUser: (userData)=>apiRequest(\"POST\", \"/users\", userData),\n    // Update user profile\n    updateUser: (userData)=>apiRequest(\"PUT\", \"/users/me\", userData),\n    // Get health profile\n    getHealthProfile: ()=>apiRequest(\"GET\", \"/users/me/health-profile\"),\n    // Create health profile\n    createHealthProfile: (profileData)=>apiRequest(\"POST\", \"/users/me/health-profile\", profileData),\n    // Update health profile\n    updateHealthProfile: (profileData)=>apiRequest(\"PUT\", \"/users/me/health-profile\", profileData),\n    // Get user settings\n    getSettings: ()=>apiRequest(\"GET\", \"/users/me/settings\"),\n    // Create/update user setting\n    updateSetting: (key, value)=>apiRequest(\"POST\", \"/users/me/settings\", {\n            setting_key: key,\n            setting_value: value\n        })\n};\n// Health Records API\nconst healthApi = {\n    // Get health records\n    getRecords: (params)=>apiRequest(\"GET\", \"/health/records\", undefined, {\n            params\n        }),\n    // Create health record\n    createRecord: (recordData)=>apiRequest(\"POST\", \"/health/records\", recordData),\n    // Get specific health record\n    getRecord: (recordId)=>apiRequest(\"GET\", \"/health/records/\".concat(recordId)),\n    // Delete health record\n    deleteRecord: (recordId)=>apiRequest(\"DELETE\", \"/health/records/\".concat(recordId)),\n    // Get health statistics\n    getStats: (recordType)=>apiRequest(\"GET\", \"/health/stats/\".concat(recordType))\n};\n// Medications API\nconst medicationsApi = {\n    // Get medications\n    getMedications: function() {\n        let activeOnly = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return apiRequest(\"GET\", \"/medications\", undefined, {\n            params: {\n                active_only: activeOnly\n            }\n        });\n    },\n    // Create medication\n    createMedication: (medicationData)=>apiRequest(\"POST\", \"/medications\", medicationData),\n    // Get specific medication\n    getMedication: (medicationId)=>apiRequest(\"GET\", \"/medications/\".concat(medicationId)),\n    // Update medication\n    updateMedication: (medicationId, medicationData)=>apiRequest(\"PUT\", \"/medications/\".concat(medicationId), medicationData),\n    // Delete medication\n    deleteMedication: (medicationId)=>apiRequest(\"DELETE\", \"/medications/\".concat(medicationId))\n};\n// Schedules API\nconst schedulesApi = {\n    // Get schedules\n    getSchedules: (params)=>apiRequest(\"GET\", \"/schedules\", undefined, {\n            params\n        }),\n    // Create schedule\n    createSchedule: (scheduleData)=>apiRequest(\"POST\", \"/schedules\", scheduleData),\n    // Get today's schedules\n    getTodaySchedules: ()=>apiRequest(\"GET\", \"/schedules/today\"),\n    // Get specific schedule\n    getSchedule: (scheduleId)=>apiRequest(\"GET\", \"/schedules/\".concat(scheduleId)),\n    // Update schedule\n    updateSchedule: (scheduleId, scheduleData)=>apiRequest(\"PUT\", \"/schedules/\".concat(scheduleId), scheduleData),\n    // Delete schedule\n    deleteSchedule: (scheduleId)=>apiRequest(\"DELETE\", \"/schedules/\".concat(scheduleId)),\n    // Get reminders\n    getReminders: (params)=>apiRequest(\"GET\", \"/schedules/reminders\", undefined, {\n            params\n        }),\n    // Mark reminder as read\n    markReminderRead: (reminderId)=>apiRequest(\"PUT\", \"/schedules/reminders/\".concat(reminderId, \"/read\"))\n};\n// Chat API\nconst chatApi = {\n    // Create chat session\n    createSession: ()=>apiRequest(\"POST\", \"/chat/sessions\"),\n    // Get active session\n    getActiveSession: ()=>apiRequest(\"GET\", \"/chat/sessions/active\"),\n    // Send message\n    sendMessage: (sessionId, content)=>apiRequest(\"POST\", \"/chat/sessions/\".concat(sessionId, \"/messages\"), {\n            content\n        }),\n    // Get chat history\n    getChatHistory: (sessionId)=>apiRequest(\"GET\", \"/chat/sessions/\".concat(sessionId, \"/messages\")),\n    // End session\n    endSession: (sessionId)=>apiRequest(\"PUT\", \"/chat/sessions/\".concat(sessionId, \"/end\"))\n};\n// Dashboard API\nconst dashboardApi = {\n    // Get dashboard stats\n    getStats: ()=>apiRequest(\"GET\", \"/dashboard/stats\"),\n    // Get recent activity\n    getRecentActivity: function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return apiRequest(\"GET\", \"/dashboard/activity\", undefined, {\n            params: {\n                limit\n            }\n        });\n    },\n    // Get health summary\n    getHealthSummary: ()=>apiRequest(\"GET\", \"/dashboard/health-summary\")\n};\n// Utility functions\nconst apiUtils = {\n    // Check API health\n    checkHealth: ()=>apiRequest(\"GET\", \"/health\"),\n    // Get API info\n    getInfo: ()=>apiRequest(\"GET\", \"/info\"),\n    // Upload file (if needed)\n    uploadFile: async (file, endpoint)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return apiRequest(\"POST\", endpoint, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n    }\n};\n// Export default API client\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api.ts\n"));

/***/ })

});