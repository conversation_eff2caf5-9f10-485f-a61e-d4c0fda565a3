"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiUtils: function() { return /* binding */ apiUtils; },\n/* harmony export */   authApi: function() { return /* binding */ authApi; },\n/* harmony export */   chatApi: function() { return /* binding */ chatApi; },\n/* harmony export */   dashboardApi: function() { return /* binding */ dashboardApi; },\n/* harmony export */   healthApi: function() { return /* binding */ healthApi; },\n/* harmony export */   medicationApi: function() { return /* binding */ medicationApi; },\n/* harmony export */   scheduleApi: function() { return /* binding */ scheduleApi; },\n/* harmony export */   userApi: function() { return /* binding */ userApi; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/**\n * API client for Elderly Health Support System\n */ \n// API configuration\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 10000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use(async (config)=>{\n        try {\n            // Get token from localStorage (simple auth)\n            if (true) {\n                const token = localStorage.getItem(\"auth_token\");\n                if (token) {\n                    config.headers.Authorization = \"Bearer \".concat(token);\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to get auth token:\", error);\n        }\n        return config;\n    }, (error)=>{\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>response, (error)=>{\n        var _error_response;\n        if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n            // Redirect to login on unauthorized\n            if (true) {\n                localStorage.removeItem(\"auth_token\");\n                window.location.href = \"/auth/login\";\n            }\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\n// API client instance\nconst apiClient = createApiClient();\n// Generic API request function\nconst apiRequest = async (method, url, data, config)=>{\n    try {\n        const response = await apiClient.request({\n            method,\n            url,\n            data,\n            ...config\n        });\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"API \".concat(method, \" \").concat(url, \" error:\"), error);\n        throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"An unexpected error occurred\");\n    }\n};\n// Auth API\nconst authApi = {\n    // Login\n    login: async (email, password)=>{\n        const response = await apiRequest(\"POST\", \"/auth/login\", {\n            email,\n            password\n        });\n        // Store token in localStorage\n        if ( true && response.token) {\n            localStorage.setItem(\"auth_token\", response.token);\n        }\n        return response;\n    },\n    // Register\n    register: async (userData)=>{\n        const response = await apiRequest(\"POST\", \"/auth/register\", userData);\n        // Store token in localStorage\n        if ( true && response.token) {\n            localStorage.setItem(\"auth_token\", response.token);\n        }\n        return response;\n    },\n    // Logout\n    logout: ()=>{\n        if (true) {\n            localStorage.removeItem(\"auth_token\");\n            window.location.href = \"/auth/login\";\n        }\n    },\n    // Check if user is authenticated\n    isAuthenticated: ()=>{\n        if (true) {\n            return !!localStorage.getItem(\"auth_token\");\n        }\n        return false;\n    }\n};\n// User API\nconst userApi = {\n    // Get current user profile\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/users/me\"),\n    // Create user profile\n    createUser: (userData)=>apiRequest(\"POST\", \"/users\", userData),\n    // Update user profile\n    updateUser: (userData)=>apiRequest(\"PUT\", \"/users/me\", userData),\n    // Get health profile\n    getHealthProfile: ()=>apiRequest(\"GET\", \"/users/me/health-profile\"),\n    // Create health profile\n    createHealthProfile: (profileData)=>apiRequest(\"POST\", \"/users/me/health-profile\", profileData),\n    // Update health profile\n    updateHealthProfile: (profileData)=>apiRequest(\"PUT\", \"/users/me/health-profile\", profileData),\n    // Get user settings\n    getSettings: ()=>apiRequest(\"GET\", \"/users/me/settings\"),\n    // Create/update user setting\n    updateSetting: (key, value)=>apiRequest(\"POST\", \"/users/me/settings\", {\n            setting_key: key,\n            setting_value: value\n        })\n};\n// Health Records API\nconst healthApi = {\n    // Get health records\n    getRecords: (params)=>apiRequest(\"GET\", \"/health/records\", undefined, {\n            params\n        }),\n    // Create health record\n    createRecord: (recordData)=>apiRequest(\"POST\", \"/health/records\", recordData),\n    // Get specific health record\n    getRecord: (recordId)=>apiRequest(\"GET\", \"/health/records/\".concat(recordId)),\n    // Delete health record\n    deleteRecord: (recordId)=>apiRequest(\"DELETE\", \"/health/records/\".concat(recordId)),\n    // Get health statistics\n    getStats: (recordType)=>apiRequest(\"GET\", \"/health/stats\", undefined, {\n            params: recordType ? {\n                record_type: recordType\n            } : undefined\n        })\n};\n// Medications API\nconst medicationApi = {\n    // Get medications\n    getMedications: function() {\n        let activeOnly = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return apiRequest(\"GET\", \"/medications\", undefined, {\n            params: {\n                active_only: activeOnly\n            }\n        });\n    },\n    // Create medication\n    createMedication: (medicationData)=>apiRequest(\"POST\", \"/medications\", medicationData),\n    // Get specific medication\n    getMedication: (medicationId)=>apiRequest(\"GET\", \"/medications/\".concat(medicationId)),\n    // Update medication\n    updateMedication: (medicationId, medicationData)=>apiRequest(\"PUT\", \"/medications/\".concat(medicationId), medicationData),\n    // Delete medication\n    deleteMedication: (medicationId)=>apiRequest(\"DELETE\", \"/medications/\".concat(medicationId))\n};\n// Schedules API\nconst scheduleApi = {\n    // Get schedules\n    getSchedules: (params)=>apiRequest(\"GET\", \"/schedules\", undefined, {\n            params\n        }),\n    // Create schedule\n    createSchedule: (scheduleData)=>apiRequest(\"POST\", \"/schedules\", scheduleData),\n    // Get today's schedules\n    getTodaySchedules: ()=>apiRequest(\"GET\", \"/schedules/today\"),\n    // Get specific schedule\n    getSchedule: (scheduleId)=>apiRequest(\"GET\", \"/schedules/\".concat(scheduleId)),\n    // Update schedule\n    updateSchedule: (scheduleId, scheduleData)=>apiRequest(\"PUT\", \"/schedules/\".concat(scheduleId), scheduleData),\n    // Delete schedule\n    deleteSchedule: (scheduleId)=>apiRequest(\"DELETE\", \"/schedules/\".concat(scheduleId)),\n    // Get reminders\n    getReminders: (params)=>apiRequest(\"GET\", \"/schedules/reminders\", undefined, {\n            params\n        }),\n    // Mark reminder as read\n    markReminderRead: (reminderId)=>apiRequest(\"PUT\", \"/schedules/reminders/\".concat(reminderId, \"/read\"))\n};\n// Chat API\nconst chatApi = {\n    // Create chat session\n    createSession: ()=>apiRequest(\"POST\", \"/chat/sessions\"),\n    // Get active session\n    getActiveSession: ()=>apiRequest(\"GET\", \"/chat/sessions/active\"),\n    // Send message\n    sendMessage: (sessionId, content)=>apiRequest(\"POST\", \"/chat/sessions/\".concat(sessionId, \"/messages\"), {\n            content\n        }),\n    // Get chat history\n    getChatHistory: (sessionId)=>apiRequest(\"GET\", \"/chat/sessions/\".concat(sessionId, \"/messages\")),\n    // End session\n    endSession: (sessionId)=>apiRequest(\"PUT\", \"/chat/sessions/\".concat(sessionId, \"/end\"))\n};\n// Dashboard API\nconst dashboardApi = {\n    // Get dashboard stats\n    getStats: ()=>apiRequest(\"GET\", \"/dashboard/stats\"),\n    // Get recent activity\n    getRecentActivity: function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return apiRequest(\"GET\", \"/dashboard/activity\", undefined, {\n            params: {\n                limit\n            }\n        });\n    },\n    // Get health summary\n    getHealthSummary: ()=>apiRequest(\"GET\", \"/dashboard/health-summary\")\n};\n// Utility functions\nconst apiUtils = {\n    // Check API health\n    checkHealth: ()=>apiRequest(\"GET\", \"/health\"),\n    // Get API info\n    getInfo: ()=>apiRequest(\"GET\", \"/info\"),\n    // Upload file (if needed)\n    uploadFile: async (file, endpoint)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return apiRequest(\"POST\", endpoint, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n    }\n};\n// Export default API client\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBOztDQUVDLEdBRStFO0FBb0JoRixvQkFBb0I7QUFDcEIsTUFBTUMsZUFBZUMsMkJBQW9DLElBQUk7QUFFN0Qsd0JBQXdCO0FBQ3hCLE1BQU1HLGtCQUFrQjtJQUN0QixNQUFNQyxTQUFTTixvREFBWSxDQUFDO1FBQzFCUSxTQUFTUDtRQUNUUSxTQUFTO1FBQ1RDLFNBQVM7WUFDUCxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLHdDQUF3QztJQUN4Q0osT0FBT0ssWUFBWSxDQUFDQyxPQUFPLENBQUNDLEdBQUcsQ0FDN0IsT0FBT0M7UUFDTCxJQUFJO1lBQ0YsNENBQTRDO1lBQzVDLElBQUksSUFBa0IsRUFBYTtnQkFDakMsTUFBTUMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO2dCQUNuQyxJQUFJRixPQUFPO29CQUNURCxPQUFPSixPQUFPLENBQUNRLGFBQWEsR0FBRyxVQUFnQixPQUFOSDtnQkFDM0M7WUFDRjtRQUNGLEVBQUUsT0FBT0ksT0FBTztZQUNkQyxRQUFRQyxJQUFJLENBQUMsNkJBQTZCRjtRQUM1QztRQUNBLE9BQU9MO0lBQ1QsR0FDQSxDQUFDSztRQUNDLE9BQU9HLFFBQVFDLE1BQU0sQ0FBQ0o7SUFDeEI7SUFHRiwwQ0FBMEM7SUFDMUNiLE9BQU9LLFlBQVksQ0FBQ2EsUUFBUSxDQUFDWCxHQUFHLENBQzlCLENBQUNXLFdBQTRCQSxVQUM3QixDQUFDTDtZQUNLQTtRQUFKLElBQUlBLEVBQUFBLGtCQUFBQSxNQUFNSyxRQUFRLGNBQWRMLHNDQUFBQSxnQkFBZ0JNLE1BQU0sTUFBSyxLQUFLO1lBQ2xDLG9DQUFvQztZQUNwQyxJQUFJLElBQWtCLEVBQWE7Z0JBQ2pDVCxhQUFhVSxVQUFVLENBQUM7Z0JBQ3hCQyxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRztZQUN6QjtRQUNGO1FBQ0EsT0FBT1AsUUFBUUMsTUFBTSxDQUFDSjtJQUN4QjtJQUdGLE9BQU9iO0FBQ1Q7QUFFQSxzQkFBc0I7QUFDdEIsTUFBTXdCLFlBQVl6QjtBQUVsQiwrQkFBK0I7QUFDL0IsTUFBTTBCLGFBQWEsT0FDakJDLFFBQ0FDLEtBQ0FDLE1BQ0FwQjtJQUVBLElBQUk7UUFDRixNQUFNVSxXQUFXLE1BQU1NLFVBQVVsQixPQUFPLENBQUk7WUFDMUNvQjtZQUNBQztZQUNBQztZQUNBLEdBQUdwQixNQUFNO1FBQ1g7UUFDQSxPQUFPVSxTQUFTVSxJQUFJO0lBQ3RCLEVBQUUsT0FBT2YsT0FBWTtZQUdqQkEsc0JBQUFBO1FBRkZDLFFBQVFELEtBQUssQ0FBQyxPQUFpQmMsT0FBVkQsUUFBTyxLQUFPLE9BQUpDLEtBQUksWUFBVWQ7UUFDN0MsTUFBTSxJQUFJZ0IsTUFDUmhCLEVBQUFBLGtCQUFBQSxNQUFNSyxRQUFRLGNBQWRMLHVDQUFBQSx1QkFBQUEsZ0JBQWdCZSxJQUFJLGNBQXBCZiwyQ0FBQUEscUJBQXNCaUIsT0FBTyxLQUM3QmpCLE1BQU1pQixPQUFPLElBQ2I7SUFFSjtBQUNGO0FBRUEsV0FBVztBQUNKLE1BQU1DLFVBQVU7SUFDckIsUUFBUTtJQUNSQyxPQUFPLE9BQU9DLE9BQWVDO1FBQzNCLE1BQU1oQixXQUFXLE1BQU1PLFdBQTBDLFFBQVEsZUFBZTtZQUN0RlE7WUFDQUM7UUFDRjtRQUVBLDhCQUE4QjtRQUM5QixJQUFJLEtBQWtCLElBQWVoQixTQUFTVCxLQUFLLEVBQUU7WUFDbkRDLGFBQWF5QixPQUFPLENBQUMsY0FBY2pCLFNBQVNULEtBQUs7UUFDbkQ7UUFFQSxPQUFPUztJQUNUO0lBRUEsV0FBVztJQUNYa0IsVUFBVSxPQUFPQztRQUNmLE1BQU1uQixXQUFXLE1BQU1PLFdBQTBDLFFBQVEsa0JBQWtCWTtRQUUzRiw4QkFBOEI7UUFDOUIsSUFBSSxLQUFrQixJQUFlbkIsU0FBU1QsS0FBSyxFQUFFO1lBQ25EQyxhQUFheUIsT0FBTyxDQUFDLGNBQWNqQixTQUFTVCxLQUFLO1FBQ25EO1FBRUEsT0FBT1M7SUFDVDtJQUVBLFNBQVM7SUFDVG9CLFFBQVE7UUFDTixJQUFJLElBQWtCLEVBQWE7WUFDakM1QixhQUFhVSxVQUFVLENBQUM7WUFDeEJDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1FBQ3pCO0lBQ0Y7SUFFQSxpQ0FBaUM7SUFDakNnQixpQkFBaUI7UUFDZixJQUFJLElBQWtCLEVBQWE7WUFDakMsT0FBTyxDQUFDLENBQUM3QixhQUFhQyxPQUFPLENBQUM7UUFDaEM7UUFDQSxPQUFPO0lBQ1Q7QUFDRixFQUFFO0FBRUYsV0FBVztBQUNKLE1BQU02QixVQUFVO0lBQ3JCLDJCQUEyQjtJQUMzQkMsZ0JBQWdCLElBQ2RoQixXQUFpQixPQUFPO0lBRTFCLHNCQUFzQjtJQUN0QmlCLFlBQVksQ0FBQ0wsV0FDWFosV0FBaUIsUUFBUSxVQUFVWTtJQUVyQyxzQkFBc0I7SUFDdEJNLFlBQVksQ0FBQ04sV0FDWFosV0FBaUIsT0FBTyxhQUFhWTtJQUV2QyxxQkFBcUI7SUFDckJPLGtCQUFrQixJQUNoQm5CLFdBQTBCLE9BQU87SUFFbkMsd0JBQXdCO0lBQ3hCb0IscUJBQXFCLENBQUNDLGNBQ3BCckIsV0FBMEIsUUFBUSw0QkFBNEJxQjtJQUVoRSx3QkFBd0I7SUFDeEJDLHFCQUFxQixDQUFDRCxjQUNwQnJCLFdBQTBCLE9BQU8sNEJBQTRCcUI7SUFFL0Qsb0JBQW9CO0lBQ3BCRSxhQUFhLElBQ1h2QixXQUFrQixPQUFPO0lBRTNCLDZCQUE2QjtJQUM3QndCLGVBQWUsQ0FBQ0MsS0FBYUMsUUFDM0IxQixXQUFnQixRQUFRLHNCQUFzQjtZQUFFMkIsYUFBYUY7WUFBS0csZUFBZUY7UUFBTTtBQUMzRixFQUFFO0FBRUYscUJBQXFCO0FBQ2QsTUFBTUcsWUFBWTtJQUN2QixxQkFBcUI7SUFDckJDLFlBQVksQ0FBQ0MsU0FPWC9CLFdBQTJCLE9BQU8sbUJBQW1CZ0MsV0FBVztZQUFFRDtRQUFPO0lBRTNFLHVCQUF1QjtJQUN2QkUsY0FBYyxDQUFDQyxhQUNibEMsV0FBeUIsUUFBUSxtQkFBbUJrQztJQUV0RCw2QkFBNkI7SUFDN0JDLFdBQVcsQ0FBQ0MsV0FDVnBDLFdBQXlCLE9BQU8sbUJBQTRCLE9BQVRvQztJQUVyRCx1QkFBdUI7SUFDdkJDLGNBQWMsQ0FBQ0QsV0FDYnBDLFdBQWlCLFVBQVUsbUJBQTRCLE9BQVRvQztJQUVoRCx3QkFBd0I7SUFDeEJFLFVBQVUsQ0FBQ0MsYUFDVHZDLFdBQWdCLE9BQU8saUJBQWlCZ0MsV0FBVztZQUNqREQsUUFBUVEsYUFBYTtnQkFBRUMsYUFBYUQ7WUFBVyxJQUFJUDtRQUNyRDtBQUNKLEVBQUU7QUFFRixrQkFBa0I7QUFDWCxNQUFNUyxnQkFBZ0I7SUFDM0Isa0JBQWtCO0lBQ2xCQyxnQkFBZ0I7WUFBQ0MsOEVBQXNCO2VBQ3JDM0MsV0FBeUIsT0FBTyxnQkFBZ0JnQyxXQUFXO1lBQ3pERCxRQUFRO2dCQUFFYSxhQUFhRDtZQUFXO1FBQ3BDOztJQUVGLG9CQUFvQjtJQUNwQkUsa0JBQWtCLENBQUNDLGlCQUNqQjlDLFdBQXVCLFFBQVEsZ0JBQWdCOEM7SUFFakQsMEJBQTBCO0lBQzFCQyxlQUFlLENBQUNDLGVBQ2RoRCxXQUF1QixPQUFPLGdCQUE2QixPQUFiZ0Q7SUFFaEQsb0JBQW9CO0lBQ3BCQyxrQkFBa0IsQ0FBQ0QsY0FBc0JGLGlCQUN2QzlDLFdBQXVCLE9BQU8sZ0JBQTZCLE9BQWJnRCxlQUFnQkY7SUFFaEUsb0JBQW9CO0lBQ3BCSSxrQkFBa0IsQ0FBQ0YsZUFDakJoRCxXQUFpQixVQUFVLGdCQUE2QixPQUFiZ0Q7QUFDL0MsRUFBRTtBQUVGLGdCQUFnQjtBQUNULE1BQU1HLGNBQWM7SUFDekIsZ0JBQWdCO0lBQ2hCQyxjQUFjLENBQUNyQixTQVFiL0IsV0FBdUIsT0FBTyxjQUFjZ0MsV0FBVztZQUFFRDtRQUFPO0lBRWxFLGtCQUFrQjtJQUNsQnNCLGdCQUFnQixDQUFDQyxlQUNmdEQsV0FBcUIsUUFBUSxjQUFjc0Q7SUFFN0Msd0JBQXdCO0lBQ3hCQyxtQkFBbUIsSUFDakJ2RCxXQUF1QixPQUFPO0lBRWhDLHdCQUF3QjtJQUN4QndELGFBQWEsQ0FBQ0MsYUFDWnpELFdBQXFCLE9BQU8sY0FBeUIsT0FBWHlEO0lBRTVDLGtCQUFrQjtJQUNsQkMsZ0JBQWdCLENBQUNELFlBQW9CSCxlQUNuQ3RELFdBQXFCLE9BQU8sY0FBeUIsT0FBWHlELGFBQWNIO0lBRTFELGtCQUFrQjtJQUNsQkssZ0JBQWdCLENBQUNGLGFBQ2Z6RCxXQUFpQixVQUFVLGNBQXlCLE9BQVh5RDtJQUUzQyxnQkFBZ0I7SUFDaEJHLGNBQWMsQ0FBQzdCLFNBSWIvQixXQUF1QixPQUFPLHdCQUF3QmdDLFdBQVc7WUFBRUQ7UUFBTztJQUU1RSx3QkFBd0I7SUFDeEI4QixrQkFBa0IsQ0FBQ0MsYUFDakI5RCxXQUFpQixPQUFPLHdCQUFtQyxPQUFYOEQsWUFBVztBQUMvRCxFQUFFO0FBRUYsV0FBVztBQUNKLE1BQU1DLFVBQVU7SUFDckIsc0JBQXNCO0lBQ3RCQyxlQUFlLElBQ2JoRSxXQUF3QixRQUFRO0lBRWxDLHFCQUFxQjtJQUNyQmlFLGtCQUFrQixJQUNoQmpFLFdBQStCLE9BQU87SUFFeEMsZUFBZTtJQUNma0UsYUFBYSxDQUFDQyxXQUFtQkMsVUFDL0JwRSxXQUF5QixRQUFRLGtCQUE0QixPQUFWbUUsV0FBVSxjQUFZO1lBQUVDO1FBQVE7SUFFckYsbUJBQW1CO0lBQ25CQyxnQkFBZ0IsQ0FBQ0YsWUFDZm5FLFdBQTBCLE9BQU8sa0JBQTRCLE9BQVZtRSxXQUFVO0lBRS9ELGNBQWM7SUFDZEcsWUFBWSxDQUFDSCxZQUNYbkUsV0FBaUIsT0FBTyxrQkFBNEIsT0FBVm1FLFdBQVU7QUFDeEQsRUFBRTtBQUVGLGdCQUFnQjtBQUNULE1BQU1JLGVBQWU7SUFDMUIsc0JBQXNCO0lBQ3RCakMsVUFBVSxJQUNSdEMsV0FBZ0IsT0FBTztJQUV6QixzQkFBc0I7SUFDdEJ3RSxtQkFBbUI7WUFBQ0MseUVBQWdCO2VBQ2xDekUsV0FBa0IsT0FBTyx1QkFBdUJnQyxXQUFXO1lBQ3pERCxRQUFRO2dCQUFFMEM7WUFBTTtRQUNsQjs7SUFFRixxQkFBcUI7SUFDckJDLGtCQUFrQixJQUNoQjFFLFdBQWdCLE9BQU87QUFDM0IsRUFBRTtBQUVGLG9CQUFvQjtBQUNiLE1BQU0yRSxXQUFXO0lBQ3RCLG1CQUFtQjtJQUNuQkMsYUFBYSxJQUNYNUUsV0FBZ0IsT0FBTztJQUV6QixlQUFlO0lBQ2Y2RSxTQUFTLElBQ1A3RSxXQUFnQixPQUFPO0lBRXpCLDBCQUEwQjtJQUMxQjhFLFlBQVksT0FBT0MsTUFBWUM7UUFDN0IsTUFBTUMsV0FBVyxJQUFJQztRQUNyQkQsU0FBU0UsTUFBTSxDQUFDLFFBQVFKO1FBRXhCLE9BQU8vRSxXQUFnQixRQUFRZ0YsVUFBVUMsVUFBVTtZQUNqRHRHLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1FBQ0Y7SUFDRjtBQUNGLEVBQUU7QUFFRiw0QkFBNEI7QUFDNUIsK0RBQWVvQixTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2xpYi9hcGkudHM/NjhhMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEFQSSBjbGllbnQgZm9yIEVsZGVybHkgSGVhbHRoIFN1cHBvcnQgU3lzdGVtXG4gKi9cblxuaW1wb3J0IGF4aW9zLCB7IEF4aW9zSW5zdGFuY2UsIEF4aW9zUmVxdWVzdENvbmZpZywgQXhpb3NSZXNwb25zZSB9IGZyb20gJ2F4aW9zJztcbmltcG9ydCB7XG4gIFVzZXIsXG4gIFVzZXJDcmVhdGUsXG4gIFVzZXJVcGRhdGUsXG4gIEhlYWx0aFByb2ZpbGUsXG4gIEhlYWx0aFByb2ZpbGVDcmVhdGUsXG4gIEhlYWx0aFJlY29yZCxcbiAgSGVhbHRoUmVjb3JkQ3JlYXRlLFxuICBNZWRpY2F0aW9uLFxuICBNZWRpY2F0aW9uQ3JlYXRlLFxuICBTY2hlZHVsZSxcbiAgU2NoZWR1bGVDcmVhdGUsXG4gIFJlbWluZGVyLFxuICBDaGF0U2Vzc2lvbixcbiAgQ2hhdE1lc3NhZ2UsXG4gIENoYXRSZXNwb25zZSxcbiAgQXBpUmVzcG9uc2UsXG59IGZyb20gJ0AvdHlwZXMnO1xuXG4vLyBBUEkgY29uZmlndXJhdGlvblxuY29uc3QgQVBJX0JBU0VfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjgwMDEvYXBpJztcblxuLy8gQ3JlYXRlIGF4aW9zIGluc3RhbmNlXG5jb25zdCBjcmVhdGVBcGlDbGllbnQgPSAoKTogQXhpb3NJbnN0YW5jZSA9PiB7XG4gIGNvbnN0IGNsaWVudCA9IGF4aW9zLmNyZWF0ZSh7XG4gICAgYmFzZVVSTDogQVBJX0JBU0VfVVJMLFxuICAgIHRpbWVvdXQ6IDEwMDAwLFxuICAgIGhlYWRlcnM6IHtcbiAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgfSxcbiAgfSk7XG5cbiAgLy8gUmVxdWVzdCBpbnRlcmNlcHRvciB0byBhZGQgYXV0aCB0b2tlblxuICBjbGllbnQuaW50ZXJjZXB0b3JzLnJlcXVlc3QudXNlKFxuICAgIGFzeW5jIChjb25maWcpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIEdldCB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZSAoc2ltcGxlIGF1dGgpXG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGhfdG9rZW4nKTtcbiAgICAgICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIGdldCBhdXRoIHRva2VuOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBjb25maWc7XG4gICAgfSxcbiAgICAoZXJyb3IpID0+IHtcbiAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XG4gICAgfVxuICApO1xuXG4gIC8vIFJlc3BvbnNlIGludGVyY2VwdG9yIGZvciBlcnJvciBoYW5kbGluZ1xuICBjbGllbnQuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcbiAgICAocmVzcG9uc2U6IEF4aW9zUmVzcG9uc2UpID0+IHJlc3BvbnNlLFxuICAgIChlcnJvcikgPT4ge1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICAvLyBSZWRpcmVjdCB0byBsb2dpbiBvbiB1bmF1dGhvcml6ZWRcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2F1dGhfdG9rZW4nKTtcbiAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvYXV0aC9sb2dpbic7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XG4gICAgfVxuICApO1xuXG4gIHJldHVybiBjbGllbnQ7XG59O1xuXG4vLyBBUEkgY2xpZW50IGluc3RhbmNlXG5jb25zdCBhcGlDbGllbnQgPSBjcmVhdGVBcGlDbGllbnQoKTtcblxuLy8gR2VuZXJpYyBBUEkgcmVxdWVzdCBmdW5jdGlvblxuY29uc3QgYXBpUmVxdWVzdCA9IGFzeW5jIDxUPihcbiAgbWV0aG9kOiAnR0VUJyB8ICdQT1NUJyB8ICdQVVQnIHwgJ0RFTEVURScsXG4gIHVybDogc3RyaW5nLFxuICBkYXRhPzogYW55LFxuICBjb25maWc/OiBBeGlvc1JlcXVlc3RDb25maWdcbik6IFByb21pc2U8VD4gPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnJlcXVlc3Q8VD4oe1xuICAgICAgbWV0aG9kLFxuICAgICAgdXJsLFxuICAgICAgZGF0YSxcbiAgICAgIC4uLmNvbmZpZyxcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoYEFQSSAke21ldGhvZH0gJHt1cmx9IGVycm9yOmAsIGVycm9yKTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fFxuICAgICAgZXJyb3IubWVzc2FnZSB8fFxuICAgICAgJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnXG4gICAgKTtcbiAgfVxufTtcblxuLy8gQXV0aCBBUElcbmV4cG9ydCBjb25zdCBhdXRoQXBpID0ge1xuICAvLyBMb2dpblxuICBsb2dpbjogYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPHsgdG9rZW46IHN0cmluZzsgdXNlcjogVXNlciB9PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlSZXF1ZXN0PHsgdG9rZW46IHN0cmluZzsgdXNlcjogVXNlciB9PignUE9TVCcsICcvYXV0aC9sb2dpbicsIHtcbiAgICAgIGVtYWlsLFxuICAgICAgcGFzc3dvcmQsXG4gICAgfSk7XG5cbiAgICAvLyBTdG9yZSB0b2tlbiBpbiBsb2NhbFN0b3JhZ2VcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgcmVzcG9uc2UudG9rZW4pIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhdXRoX3Rva2VuJywgcmVzcG9uc2UudG9rZW4pO1xuICAgIH1cblxuICAgIHJldHVybiByZXNwb25zZTtcbiAgfSxcblxuICAvLyBSZWdpc3RlclxuICByZWdpc3RlcjogYXN5bmMgKHVzZXJEYXRhOiBVc2VyQ3JlYXRlICYgeyBwYXNzd29yZDogc3RyaW5nIH0pOiBQcm9taXNlPHsgdG9rZW46IHN0cmluZzsgdXNlcjogVXNlciB9PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlSZXF1ZXN0PHsgdG9rZW46IHN0cmluZzsgdXNlcjogVXNlciB9PignUE9TVCcsICcvYXV0aC9yZWdpc3RlcicsIHVzZXJEYXRhKTtcblxuICAgIC8vIFN0b3JlIHRva2VuIGluIGxvY2FsU3RvcmFnZVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiByZXNwb25zZS50b2tlbikge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2F1dGhfdG9rZW4nLCByZXNwb25zZS50b2tlbik7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9LFxuXG4gIC8vIExvZ291dFxuICBsb2dvdXQ6ICgpOiB2b2lkID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoX3Rva2VuJyk7XG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvYXV0aC9sb2dpbic7XG4gICAgfVxuICB9LFxuXG4gIC8vIENoZWNrIGlmIHVzZXIgaXMgYXV0aGVudGljYXRlZFxuICBpc0F1dGhlbnRpY2F0ZWQ6ICgpOiBib29sZWFuID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHJldHVybiAhIWxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhdXRoX3Rva2VuJyk7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfSxcbn07XG5cbi8vIFVzZXIgQVBJXG5leHBvcnQgY29uc3QgdXNlckFwaSA9IHtcbiAgLy8gR2V0IGN1cnJlbnQgdXNlciBwcm9maWxlXG4gIGdldEN1cnJlbnRVc2VyOiAoKTogUHJvbWlzZTxVc2VyPiA9PlxuICAgIGFwaVJlcXVlc3Q8VXNlcj4oJ0dFVCcsICcvdXNlcnMvbWUnKSxcblxuICAvLyBDcmVhdGUgdXNlciBwcm9maWxlXG4gIGNyZWF0ZVVzZXI6ICh1c2VyRGF0YTogVXNlckNyZWF0ZSk6IFByb21pc2U8VXNlcj4gPT5cbiAgICBhcGlSZXF1ZXN0PFVzZXI+KCdQT1NUJywgJy91c2VycycsIHVzZXJEYXRhKSxcblxuICAvLyBVcGRhdGUgdXNlciBwcm9maWxlXG4gIHVwZGF0ZVVzZXI6ICh1c2VyRGF0YTogVXNlclVwZGF0ZSk6IFByb21pc2U8VXNlcj4gPT5cbiAgICBhcGlSZXF1ZXN0PFVzZXI+KCdQVVQnLCAnL3VzZXJzL21lJywgdXNlckRhdGEpLFxuXG4gIC8vIEdldCBoZWFsdGggcHJvZmlsZVxuICBnZXRIZWFsdGhQcm9maWxlOiAoKTogUHJvbWlzZTxIZWFsdGhQcm9maWxlPiA9PlxuICAgIGFwaVJlcXVlc3Q8SGVhbHRoUHJvZmlsZT4oJ0dFVCcsICcvdXNlcnMvbWUvaGVhbHRoLXByb2ZpbGUnKSxcblxuICAvLyBDcmVhdGUgaGVhbHRoIHByb2ZpbGVcbiAgY3JlYXRlSGVhbHRoUHJvZmlsZTogKHByb2ZpbGVEYXRhOiBIZWFsdGhQcm9maWxlQ3JlYXRlKTogUHJvbWlzZTxIZWFsdGhQcm9maWxlPiA9PlxuICAgIGFwaVJlcXVlc3Q8SGVhbHRoUHJvZmlsZT4oJ1BPU1QnLCAnL3VzZXJzL21lL2hlYWx0aC1wcm9maWxlJywgcHJvZmlsZURhdGEpLFxuXG4gIC8vIFVwZGF0ZSBoZWFsdGggcHJvZmlsZVxuICB1cGRhdGVIZWFsdGhQcm9maWxlOiAocHJvZmlsZURhdGE6IEhlYWx0aFByb2ZpbGVDcmVhdGUpOiBQcm9taXNlPEhlYWx0aFByb2ZpbGU+ID0+XG4gICAgYXBpUmVxdWVzdDxIZWFsdGhQcm9maWxlPignUFVUJywgJy91c2Vycy9tZS9oZWFsdGgtcHJvZmlsZScsIHByb2ZpbGVEYXRhKSxcblxuICAvLyBHZXQgdXNlciBzZXR0aW5nc1xuICBnZXRTZXR0aW5nczogKCk6IFByb21pc2U8YW55W10+ID0+XG4gICAgYXBpUmVxdWVzdDxhbnlbXT4oJ0dFVCcsICcvdXNlcnMvbWUvc2V0dGluZ3MnKSxcblxuICAvLyBDcmVhdGUvdXBkYXRlIHVzZXIgc2V0dGluZ1xuICB1cGRhdGVTZXR0aW5nOiAoa2V5OiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcpOiBQcm9taXNlPGFueT4gPT5cbiAgICBhcGlSZXF1ZXN0PGFueT4oJ1BPU1QnLCAnL3VzZXJzL21lL3NldHRpbmdzJywgeyBzZXR0aW5nX2tleToga2V5LCBzZXR0aW5nX3ZhbHVlOiB2YWx1ZSB9KSxcbn07XG5cbi8vIEhlYWx0aCBSZWNvcmRzIEFQSVxuZXhwb3J0IGNvbnN0IGhlYWx0aEFwaSA9IHtcbiAgLy8gR2V0IGhlYWx0aCByZWNvcmRzXG4gIGdldFJlY29yZHM6IChwYXJhbXM/OiB7XG4gICAgcmVjb3JkX3R5cGU/OiBzdHJpbmc7XG4gICAgbGltaXQ/OiBudW1iZXI7XG4gICAgb2Zmc2V0PzogbnVtYmVyO1xuICAgIHN0YXJ0X2RhdGU/OiBzdHJpbmc7XG4gICAgZW5kX2RhdGU/OiBzdHJpbmc7XG4gIH0pOiBQcm9taXNlPEhlYWx0aFJlY29yZFtdPiA9PlxuICAgIGFwaVJlcXVlc3Q8SGVhbHRoUmVjb3JkW10+KCdHRVQnLCAnL2hlYWx0aC9yZWNvcmRzJywgdW5kZWZpbmVkLCB7IHBhcmFtcyB9KSxcblxuICAvLyBDcmVhdGUgaGVhbHRoIHJlY29yZFxuICBjcmVhdGVSZWNvcmQ6IChyZWNvcmREYXRhOiBIZWFsdGhSZWNvcmRDcmVhdGUpOiBQcm9taXNlPEhlYWx0aFJlY29yZD4gPT5cbiAgICBhcGlSZXF1ZXN0PEhlYWx0aFJlY29yZD4oJ1BPU1QnLCAnL2hlYWx0aC9yZWNvcmRzJywgcmVjb3JkRGF0YSksXG5cbiAgLy8gR2V0IHNwZWNpZmljIGhlYWx0aCByZWNvcmRcbiAgZ2V0UmVjb3JkOiAocmVjb3JkSWQ6IG51bWJlcik6IFByb21pc2U8SGVhbHRoUmVjb3JkPiA9PlxuICAgIGFwaVJlcXVlc3Q8SGVhbHRoUmVjb3JkPignR0VUJywgYC9oZWFsdGgvcmVjb3Jkcy8ke3JlY29yZElkfWApLFxuXG4gIC8vIERlbGV0ZSBoZWFsdGggcmVjb3JkXG4gIGRlbGV0ZVJlY29yZDogKHJlY29yZElkOiBudW1iZXIpOiBQcm9taXNlPHZvaWQ+ID0+XG4gICAgYXBpUmVxdWVzdDx2b2lkPignREVMRVRFJywgYC9oZWFsdGgvcmVjb3Jkcy8ke3JlY29yZElkfWApLFxuXG4gIC8vIEdldCBoZWFsdGggc3RhdGlzdGljc1xuICBnZXRTdGF0czogKHJlY29yZFR5cGU/OiBzdHJpbmcpOiBQcm9taXNlPGFueT4gPT5cbiAgICBhcGlSZXF1ZXN0PGFueT4oJ0dFVCcsICcvaGVhbHRoL3N0YXRzJywgdW5kZWZpbmVkLCB7XG4gICAgICBwYXJhbXM6IHJlY29yZFR5cGUgPyB7IHJlY29yZF90eXBlOiByZWNvcmRUeXBlIH0gOiB1bmRlZmluZWRcbiAgICB9KSxcbn07XG5cbi8vIE1lZGljYXRpb25zIEFQSVxuZXhwb3J0IGNvbnN0IG1lZGljYXRpb25BcGkgPSB7XG4gIC8vIEdldCBtZWRpY2F0aW9uc1xuICBnZXRNZWRpY2F0aW9uczogKGFjdGl2ZU9ubHk6IGJvb2xlYW4gPSB0cnVlKTogUHJvbWlzZTxNZWRpY2F0aW9uW10+ID0+XG4gICAgYXBpUmVxdWVzdDxNZWRpY2F0aW9uW10+KCdHRVQnLCAnL21lZGljYXRpb25zJywgdW5kZWZpbmVkLCB7XG4gICAgICBwYXJhbXM6IHsgYWN0aXZlX29ubHk6IGFjdGl2ZU9ubHkgfVxuICAgIH0pLFxuXG4gIC8vIENyZWF0ZSBtZWRpY2F0aW9uXG4gIGNyZWF0ZU1lZGljYXRpb246IChtZWRpY2F0aW9uRGF0YTogTWVkaWNhdGlvbkNyZWF0ZSk6IFByb21pc2U8TWVkaWNhdGlvbj4gPT5cbiAgICBhcGlSZXF1ZXN0PE1lZGljYXRpb24+KCdQT1NUJywgJy9tZWRpY2F0aW9ucycsIG1lZGljYXRpb25EYXRhKSxcblxuICAvLyBHZXQgc3BlY2lmaWMgbWVkaWNhdGlvblxuICBnZXRNZWRpY2F0aW9uOiAobWVkaWNhdGlvbklkOiBudW1iZXIpOiBQcm9taXNlPE1lZGljYXRpb24+ID0+XG4gICAgYXBpUmVxdWVzdDxNZWRpY2F0aW9uPignR0VUJywgYC9tZWRpY2F0aW9ucy8ke21lZGljYXRpb25JZH1gKSxcblxuICAvLyBVcGRhdGUgbWVkaWNhdGlvblxuICB1cGRhdGVNZWRpY2F0aW9uOiAobWVkaWNhdGlvbklkOiBudW1iZXIsIG1lZGljYXRpb25EYXRhOiBQYXJ0aWFsPE1lZGljYXRpb25DcmVhdGU+KTogUHJvbWlzZTxNZWRpY2F0aW9uPiA9PlxuICAgIGFwaVJlcXVlc3Q8TWVkaWNhdGlvbj4oJ1BVVCcsIGAvbWVkaWNhdGlvbnMvJHttZWRpY2F0aW9uSWR9YCwgbWVkaWNhdGlvbkRhdGEpLFxuXG4gIC8vIERlbGV0ZSBtZWRpY2F0aW9uXG4gIGRlbGV0ZU1lZGljYXRpb246IChtZWRpY2F0aW9uSWQ6IG51bWJlcik6IFByb21pc2U8dm9pZD4gPT5cbiAgICBhcGlSZXF1ZXN0PHZvaWQ+KCdERUxFVEUnLCBgL21lZGljYXRpb25zLyR7bWVkaWNhdGlvbklkfWApLFxufTtcblxuLy8gU2NoZWR1bGVzIEFQSVxuZXhwb3J0IGNvbnN0IHNjaGVkdWxlQXBpID0ge1xuICAvLyBHZXQgc2NoZWR1bGVzXG4gIGdldFNjaGVkdWxlczogKHBhcmFtcz86IHtcbiAgICBzY2hlZHVsZV90eXBlPzogc3RyaW5nO1xuICAgIHVwY29taW5nX29ubHk/OiBib29sZWFuO1xuICAgIHN0YXJ0X2RhdGU/OiBzdHJpbmc7XG4gICAgZW5kX2RhdGU/OiBzdHJpbmc7XG4gICAgbGltaXQ/OiBudW1iZXI7XG4gICAgb2Zmc2V0PzogbnVtYmVyO1xuICB9KTogUHJvbWlzZTxTY2hlZHVsZVtdPiA9PlxuICAgIGFwaVJlcXVlc3Q8U2NoZWR1bGVbXT4oJ0dFVCcsICcvc2NoZWR1bGVzJywgdW5kZWZpbmVkLCB7IHBhcmFtcyB9KSxcblxuICAvLyBDcmVhdGUgc2NoZWR1bGVcbiAgY3JlYXRlU2NoZWR1bGU6IChzY2hlZHVsZURhdGE6IFNjaGVkdWxlQ3JlYXRlKTogUHJvbWlzZTxTY2hlZHVsZT4gPT5cbiAgICBhcGlSZXF1ZXN0PFNjaGVkdWxlPignUE9TVCcsICcvc2NoZWR1bGVzJywgc2NoZWR1bGVEYXRhKSxcblxuICAvLyBHZXQgdG9kYXkncyBzY2hlZHVsZXNcbiAgZ2V0VG9kYXlTY2hlZHVsZXM6ICgpOiBQcm9taXNlPFNjaGVkdWxlW10+ID0+XG4gICAgYXBpUmVxdWVzdDxTY2hlZHVsZVtdPignR0VUJywgJy9zY2hlZHVsZXMvdG9kYXknKSxcblxuICAvLyBHZXQgc3BlY2lmaWMgc2NoZWR1bGVcbiAgZ2V0U2NoZWR1bGU6IChzY2hlZHVsZUlkOiBudW1iZXIpOiBQcm9taXNlPFNjaGVkdWxlPiA9PlxuICAgIGFwaVJlcXVlc3Q8U2NoZWR1bGU+KCdHRVQnLCBgL3NjaGVkdWxlcy8ke3NjaGVkdWxlSWR9YCksXG5cbiAgLy8gVXBkYXRlIHNjaGVkdWxlXG4gIHVwZGF0ZVNjaGVkdWxlOiAoc2NoZWR1bGVJZDogbnVtYmVyLCBzY2hlZHVsZURhdGE6IFBhcnRpYWw8U2NoZWR1bGVDcmVhdGU+KTogUHJvbWlzZTxTY2hlZHVsZT4gPT5cbiAgICBhcGlSZXF1ZXN0PFNjaGVkdWxlPignUFVUJywgYC9zY2hlZHVsZXMvJHtzY2hlZHVsZUlkfWAsIHNjaGVkdWxlRGF0YSksXG5cbiAgLy8gRGVsZXRlIHNjaGVkdWxlXG4gIGRlbGV0ZVNjaGVkdWxlOiAoc2NoZWR1bGVJZDogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiA9PlxuICAgIGFwaVJlcXVlc3Q8dm9pZD4oJ0RFTEVURScsIGAvc2NoZWR1bGVzLyR7c2NoZWR1bGVJZH1gKSxcblxuICAvLyBHZXQgcmVtaW5kZXJzXG4gIGdldFJlbWluZGVyczogKHBhcmFtcz86IHtcbiAgICB1cGNvbWluZ19vbmx5PzogYm9vbGVhbjtcbiAgICBsaW1pdD86IG51bWJlcjtcbiAgfSk6IFByb21pc2U8UmVtaW5kZXJbXT4gPT5cbiAgICBhcGlSZXF1ZXN0PFJlbWluZGVyW10+KCdHRVQnLCAnL3NjaGVkdWxlcy9yZW1pbmRlcnMnLCB1bmRlZmluZWQsIHsgcGFyYW1zIH0pLFxuXG4gIC8vIE1hcmsgcmVtaW5kZXIgYXMgcmVhZFxuICBtYXJrUmVtaW5kZXJSZWFkOiAocmVtaW5kZXJJZDogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiA9PlxuICAgIGFwaVJlcXVlc3Q8dm9pZD4oJ1BVVCcsIGAvc2NoZWR1bGVzL3JlbWluZGVycy8ke3JlbWluZGVySWR9L3JlYWRgKSxcbn07XG5cbi8vIENoYXQgQVBJXG5leHBvcnQgY29uc3QgY2hhdEFwaSA9IHtcbiAgLy8gQ3JlYXRlIGNoYXQgc2Vzc2lvblxuICBjcmVhdGVTZXNzaW9uOiAoKTogUHJvbWlzZTxDaGF0U2Vzc2lvbj4gPT5cbiAgICBhcGlSZXF1ZXN0PENoYXRTZXNzaW9uPignUE9TVCcsICcvY2hhdC9zZXNzaW9ucycpLFxuXG4gIC8vIEdldCBhY3RpdmUgc2Vzc2lvblxuICBnZXRBY3RpdmVTZXNzaW9uOiAoKTogUHJvbWlzZTxDaGF0U2Vzc2lvbiB8IG51bGw+ID0+XG4gICAgYXBpUmVxdWVzdDxDaGF0U2Vzc2lvbiB8IG51bGw+KCdHRVQnLCAnL2NoYXQvc2Vzc2lvbnMvYWN0aXZlJyksXG5cbiAgLy8gU2VuZCBtZXNzYWdlXG4gIHNlbmRNZXNzYWdlOiAoc2Vzc2lvbklkOiBudW1iZXIsIGNvbnRlbnQ6IHN0cmluZyk6IFByb21pc2U8Q2hhdFJlc3BvbnNlPiA9PlxuICAgIGFwaVJlcXVlc3Q8Q2hhdFJlc3BvbnNlPignUE9TVCcsIGAvY2hhdC9zZXNzaW9ucy8ke3Nlc3Npb25JZH0vbWVzc2FnZXNgLCB7IGNvbnRlbnQgfSksXG5cbiAgLy8gR2V0IGNoYXQgaGlzdG9yeVxuICBnZXRDaGF0SGlzdG9yeTogKHNlc3Npb25JZDogbnVtYmVyKTogUHJvbWlzZTxDaGF0TWVzc2FnZVtdPiA9PlxuICAgIGFwaVJlcXVlc3Q8Q2hhdE1lc3NhZ2VbXT4oJ0dFVCcsIGAvY2hhdC9zZXNzaW9ucy8ke3Nlc3Npb25JZH0vbWVzc2FnZXNgKSxcblxuICAvLyBFbmQgc2Vzc2lvblxuICBlbmRTZXNzaW9uOiAoc2Vzc2lvbklkOiBudW1iZXIpOiBQcm9taXNlPHZvaWQ+ID0+XG4gICAgYXBpUmVxdWVzdDx2b2lkPignUFVUJywgYC9jaGF0L3Nlc3Npb25zLyR7c2Vzc2lvbklkfS9lbmRgKSxcbn07XG5cbi8vIERhc2hib2FyZCBBUElcbmV4cG9ydCBjb25zdCBkYXNoYm9hcmRBcGkgPSB7XG4gIC8vIEdldCBkYXNoYm9hcmQgc3RhdHNcbiAgZ2V0U3RhdHM6ICgpOiBQcm9taXNlPGFueT4gPT5cbiAgICBhcGlSZXF1ZXN0PGFueT4oJ0dFVCcsICcvZGFzaGJvYXJkL3N0YXRzJyksXG5cbiAgLy8gR2V0IHJlY2VudCBhY3Rpdml0eVxuICBnZXRSZWNlbnRBY3Rpdml0eTogKGxpbWl0OiBudW1iZXIgPSAxMCk6IFByb21pc2U8YW55W10+ID0+XG4gICAgYXBpUmVxdWVzdDxhbnlbXT4oJ0dFVCcsICcvZGFzaGJvYXJkL2FjdGl2aXR5JywgdW5kZWZpbmVkLCB7XG4gICAgICBwYXJhbXM6IHsgbGltaXQgfVxuICAgIH0pLFxuXG4gIC8vIEdldCBoZWFsdGggc3VtbWFyeVxuICBnZXRIZWFsdGhTdW1tYXJ5OiAoKTogUHJvbWlzZTxhbnk+ID0+XG4gICAgYXBpUmVxdWVzdDxhbnk+KCdHRVQnLCAnL2Rhc2hib2FyZC9oZWFsdGgtc3VtbWFyeScpLFxufTtcblxuLy8gVXRpbGl0eSBmdW5jdGlvbnNcbmV4cG9ydCBjb25zdCBhcGlVdGlscyA9IHtcbiAgLy8gQ2hlY2sgQVBJIGhlYWx0aFxuICBjaGVja0hlYWx0aDogKCk6IFByb21pc2U8YW55PiA9PlxuICAgIGFwaVJlcXVlc3Q8YW55PignR0VUJywgJy9oZWFsdGgnKSxcblxuICAvLyBHZXQgQVBJIGluZm9cbiAgZ2V0SW5mbzogKCk6IFByb21pc2U8YW55PiA9PlxuICAgIGFwaVJlcXVlc3Q8YW55PignR0VUJywgJy9pbmZvJyksXG5cbiAgLy8gVXBsb2FkIGZpbGUgKGlmIG5lZWRlZClcbiAgdXBsb2FkRmlsZTogYXN5bmMgKGZpbGU6IEZpbGUsIGVuZHBvaW50OiBzdHJpbmcpOiBQcm9taXNlPGFueT4gPT4ge1xuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG4gICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSk7XG5cbiAgICByZXR1cm4gYXBpUmVxdWVzdDxhbnk+KCdQT1NUJywgZW5kcG9pbnQsIGZvcm1EYXRhLCB7XG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScsXG4gICAgICB9LFxuICAgIH0pO1xuICB9LFxufTtcblxuLy8gRXhwb3J0IGRlZmF1bHQgQVBJIGNsaWVudFxuZXhwb3J0IGRlZmF1bHQgYXBpQ2xpZW50O1xuIl0sIm5hbWVzIjpbImF4aW9zIiwiQVBJX0JBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCIsImNyZWF0ZUFwaUNsaWVudCIsImNsaWVudCIsImNyZWF0ZSIsImJhc2VVUkwiLCJ0aW1lb3V0IiwiaGVhZGVycyIsImludGVyY2VwdG9ycyIsInJlcXVlc3QiLCJ1c2UiLCJjb25maWciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJBdXRob3JpemF0aW9uIiwiZXJyb3IiLCJjb25zb2xlIiwid2FybiIsIlByb21pc2UiLCJyZWplY3QiLCJyZXNwb25zZSIsInN0YXR1cyIsInJlbW92ZUl0ZW0iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJhcGlDbGllbnQiLCJhcGlSZXF1ZXN0IiwibWV0aG9kIiwidXJsIiwiZGF0YSIsIkVycm9yIiwibWVzc2FnZSIsImF1dGhBcGkiLCJsb2dpbiIsImVtYWlsIiwicGFzc3dvcmQiLCJzZXRJdGVtIiwicmVnaXN0ZXIiLCJ1c2VyRGF0YSIsImxvZ291dCIsImlzQXV0aGVudGljYXRlZCIsInVzZXJBcGkiLCJnZXRDdXJyZW50VXNlciIsImNyZWF0ZVVzZXIiLCJ1cGRhdGVVc2VyIiwiZ2V0SGVhbHRoUHJvZmlsZSIsImNyZWF0ZUhlYWx0aFByb2ZpbGUiLCJwcm9maWxlRGF0YSIsInVwZGF0ZUhlYWx0aFByb2ZpbGUiLCJnZXRTZXR0aW5ncyIsInVwZGF0ZVNldHRpbmciLCJrZXkiLCJ2YWx1ZSIsInNldHRpbmdfa2V5Iiwic2V0dGluZ192YWx1ZSIsImhlYWx0aEFwaSIsImdldFJlY29yZHMiLCJwYXJhbXMiLCJ1bmRlZmluZWQiLCJjcmVhdGVSZWNvcmQiLCJyZWNvcmREYXRhIiwiZ2V0UmVjb3JkIiwicmVjb3JkSWQiLCJkZWxldGVSZWNvcmQiLCJnZXRTdGF0cyIsInJlY29yZFR5cGUiLCJyZWNvcmRfdHlwZSIsIm1lZGljYXRpb25BcGkiLCJnZXRNZWRpY2F0aW9ucyIsImFjdGl2ZU9ubHkiLCJhY3RpdmVfb25seSIsImNyZWF0ZU1lZGljYXRpb24iLCJtZWRpY2F0aW9uRGF0YSIsImdldE1lZGljYXRpb24iLCJtZWRpY2F0aW9uSWQiLCJ1cGRhdGVNZWRpY2F0aW9uIiwiZGVsZXRlTWVkaWNhdGlvbiIsInNjaGVkdWxlQXBpIiwiZ2V0U2NoZWR1bGVzIiwiY3JlYXRlU2NoZWR1bGUiLCJzY2hlZHVsZURhdGEiLCJnZXRUb2RheVNjaGVkdWxlcyIsImdldFNjaGVkdWxlIiwic2NoZWR1bGVJZCIsInVwZGF0ZVNjaGVkdWxlIiwiZGVsZXRlU2NoZWR1bGUiLCJnZXRSZW1pbmRlcnMiLCJtYXJrUmVtaW5kZXJSZWFkIiwicmVtaW5kZXJJZCIsImNoYXRBcGkiLCJjcmVhdGVTZXNzaW9uIiwiZ2V0QWN0aXZlU2Vzc2lvbiIsInNlbmRNZXNzYWdlIiwic2Vzc2lvbklkIiwiY29udGVudCIsImdldENoYXRIaXN0b3J5IiwiZW5kU2Vzc2lvbiIsImRhc2hib2FyZEFwaSIsImdldFJlY2VudEFjdGl2aXR5IiwibGltaXQiLCJnZXRIZWFsdGhTdW1tYXJ5IiwiYXBpVXRpbHMiLCJjaGVja0hlYWx0aCIsImdldEluZm8iLCJ1cGxvYWRGaWxlIiwiZmlsZSIsImVuZHBvaW50IiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsImFwcGVuZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./lib/api.ts\n"));

/***/ })

});