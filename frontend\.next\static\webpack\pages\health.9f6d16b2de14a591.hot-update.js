"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/health",{

/***/ "./pages/health/index.tsx":
/*!********************************!*\
  !*** ./pages/health/index.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,PlusIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,PlusIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst HealthPage = ()=>{\n    _s();\n    const [records, setRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const healthTypes = [\n        {\n            type: \"blood_pressure\",\n            name: \"Huyết \\xe1p\",\n            unit: \"mmHg\",\n            description: \"Ghi nhận chỉ số huyết \\xe1p h\\xe0ng ng\\xe0y\",\n            color: \"bg-red-50 border-red-200 text-red-800\",\n            icon: \"❤️\"\n        },\n        {\n            type: \"blood_sugar\",\n            name: \"Đường huyết\",\n            unit: \"mg/dL\",\n            description: \"Theo d\\xf5i mức đường huyết\",\n            color: \"bg-blue-50 border-blue-200 text-blue-800\",\n            icon: \"\\uD83E\\uDE78\"\n        },\n        {\n            type: \"weight\",\n            name: \"C\\xe2n nặng\",\n            unit: \"kg\",\n            description: \"Theo d\\xf5i c\\xe2n nặng\",\n            color: \"bg-green-50 border-green-200 text-green-800\",\n            icon: \"⚖️\"\n        },\n        {\n            type: \"heart_rate\",\n            name: \"Nhịp tim\",\n            unit: \"bpm\",\n            description: \"Theo d\\xf5i nhịp tim\",\n            color: \"bg-purple-50 border-purple-200 text-purple-800\",\n            icon: \"\\uD83D\\uDC93\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadHealthData();\n    }, []);\n    const loadHealthData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Load recent records\n            const recentRecords = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.healthApi.getRecords({\n                limit: 20\n            });\n            setRecords(recentRecords);\n            // Load stats for each health type\n            const statsPromises = healthTypes.map(async (type)=>{\n                try {\n                    return await _lib_api__WEBPACK_IMPORTED_MODULE_4__.healthApi.getStats(type.type);\n                } catch (e) {\n                    return {\n                        record_type: type.type,\n                        total_records: 0,\n                        latest_value: null,\n                        latest_date: null,\n                        average_last_7_days: null,\n                        trend: \"stable\"\n                    };\n                }\n            });\n            const statsResults = await Promise.all(statsPromises);\n            setStats(statsResults);\n        } catch (err) {\n            console.error(\"Error loading health data:\", err);\n            setError(\"Kh\\xf4ng thể tải dữ liệu sức khỏe\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddRecord = (type)=>{\n        setSelectedType(type);\n        setShowAddForm(true);\n    };\n    const getTypeConfig = (type)=>{\n        return healthTypes.find((t)=>t.type === type) || healthTypes[0];\n    };\n    const getTypeStats = (type)=>{\n        return stats.find((s)=>s.record_type === type);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"Theo d\\xf5i sức khỏe\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Theo d\\xf5i sức khỏe\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-elderly-text\",\n                            children: \"Theo d\\xf5i sức khỏe\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"btn btn-primary flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PlusIcon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ghi nhận mới\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadHealthData,\n                            className: \"mt-2 text-red-600 hover:text-red-800 text-sm underline\",\n                            children: \"Thử lại\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: healthTypes.map((type)=>{\n                        const typeStats = getTypeStats(type.type);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: type.icon\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: type.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon, {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light text-sm mb-4\",\n                                    children: type.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, undefined),\n                                typeStats && typeStats.latest_value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-elderly-text\",\n                                            children: typeStats.latest_value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-elderly-text-light\",\n                                            children: typeStats.latest_date ? new Date(typeStats.latest_date).toLocaleDateString(\"vi-VN\") : \"Chưa c\\xf3 dữ liệu\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-elderly-text-light\",\n                                            children: [\n                                                \"Tổng: \",\n                                                typeStats.total_records,\n                                                \" lần ghi nhận\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: \"Chưa c\\xf3 dữ liệu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleAddRecord(type.type),\n                                    className: \"btn btn-primary w-full\",\n                                    children: \"Ghi nhận\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, type.type, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Ghi nhận gần đ\\xe2y\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined),\n                        records.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: records.slice(0, 10).map((record)=>{\n                                const typeConfig = getTypeConfig(record.record_type);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-lg \".concat(typeConfig.color),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            typeConfig.icon,\n                                                            \" \",\n                                                            typeConfig.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-bold\",\n                                                        children: record.display_value\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: new Date(record.recorded_at).toLocaleDateString(\"vi-VN\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs\",\n                                                        children: new Date(record.recorded_at).toLocaleTimeString(\"vi-VN\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block px-2 py-1 rounded-full text-xs \".concat(record.is_normal ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                        children: record.is_normal ? \"B\\xecnh thường\" : \"Cần ch\\xfa \\xfd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, record.id, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Chưa c\\xf3 ghi nhận n\\xe0o\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddForm(true),\n                                    className: \"mt-4 btn btn-primary\",\n                                    children: \"Th\\xeam ghi nhận đầu ti\\xean\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HealthPage, \"zolw/uRPK8REVhDJw+vLVnCg1Ok=\");\n_c = HealthPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(HealthPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"HealthPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/health/index.tsx\n"));

/***/ })

});