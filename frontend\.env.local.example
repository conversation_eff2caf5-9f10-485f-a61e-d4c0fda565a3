# Auth0 Configuration
AUTH0_SECRET='use [openssl rand -hex 32] to generate a 32 bytes value'
AUTH0_BASE_URL='http://localhost:3000'
AUTH0_ISSUER_BASE_URL='https://your-domain.auth0.com'
AUTH0_CLIENT_ID='your-auth0-client-id'
AUTH0_CLIENT_SECRET='your-auth0-client-secret'
AUTH0_AUDIENCE='your-auth0-api-audience'

# API Configuration
NEXT_PUBLIC_API_URL='http://localhost:8000'
NEXT_PUBLIC_API_BASE_URL='http://localhost:8000/api'

# Application Configuration
NEXT_PUBLIC_APP_NAME='Hệ thống hỗ trợ sức khỏe người cao tuổi'
NEXT_PUBLIC_APP_VERSION='1.0.0'
NEXT_PUBLIC_APP_DESCRIPTION='Hệ thống theo dõi và chăm sóc sức khỏe cho người cao tuổi'

# Environment
NODE_ENV='development'
NEXT_PUBLIC_ENVIRONMENT='development'

# Feature Flags
NEXT_PUBLIC_ENABLE_CHAT='true'
NEXT_PUBLIC_ENABLE_NOTIFICATIONS='true'
NEXT_PUBLIC_ENABLE_ANALYTICS='false'

# External Services
NEXT_PUBLIC_OPENAI_API_KEY='your-openai-api-key'
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID='your-ga-id'

# Database (for client-side display only)
NEXT_PUBLIC_DB_NAME='elderly_health_db'

# Logging
NEXT_PUBLIC_LOG_LEVEL='info'

# UI Configuration
NEXT_PUBLIC_DEFAULT_LANGUAGE='vi'
NEXT_PUBLIC_THEME='light'
NEXT_PUBLIC_FONT_SIZE='large'

# Health Metrics Configuration
NEXT_PUBLIC_BLOOD_PRESSURE_NORMAL_MAX='120/80'
NEXT_PUBLIC_HEART_RATE_NORMAL_RANGE='60-100'
NEXT_PUBLIC_BLOOD_SUGAR_NORMAL_RANGE='70-140'

# Notification Settings
NEXT_PUBLIC_REMINDER_ADVANCE_MINUTES='30'
NEXT_PUBLIC_NOTIFICATION_SOUND='true'

# Chart Configuration
NEXT_PUBLIC_CHART_THEME='light'
NEXT_PUBLIC_CHART_ANIMATION='true'

# Security
NEXT_PUBLIC_SESSION_TIMEOUT='3600'
NEXT_PUBLIC_CSRF_PROTECTION='true'

# Performance
NEXT_PUBLIC_CACHE_DURATION='300'
NEXT_PUBLIC_IMAGE_OPTIMIZATION='true'
