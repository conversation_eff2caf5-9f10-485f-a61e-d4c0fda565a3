import React, { useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { healthApi } from "@/lib/api";

interface AddHealthRecordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  selectedType?: string;
}

const healthTypes = [
  {
    type: "blood_pressure",
    name: "<PERSON><PERSON><PERSON><PERSON> á<PERSON>",
    unit: "mmHg",
    placeholder: "120/80",
    icon: "❤️",
  },
  {
    type: "blood_sugar",
    name: "<PERSON><PERSON><PERSON><PERSON> huyết",
    unit: "mg/dL",
    placeholder: "100",
    icon: "🩸",
  },
  {
    type: "weight",
    name: "Cân nặng",
    unit: "kg",
    placeholder: "65.5",
    icon: "⚖️",
  },
  {
    type: "heart_rate",
    name: "Nhịp tim",
    unit: "bpm",
    placeholder: "72",
    icon: "💓",
  },
];

const AddHealthRecordModal: React.FC<AddHealthRecordModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  selectedType = "",
}) => {
  const [formData, setFormData] = useState({
    record_type: selectedType,
    value: "",
    notes: "",
    recorded_at: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  React.useEffect(() => {
    if (selectedType) {
      setFormData(prev => ({ ...prev, record_type: selectedType }));
    }
  }, [selectedType]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.record_type || !formData.value) {
      setError("Vui lòng điền đầy đủ thông tin");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      await healthApi.createRecord({
        record_type: formData.record_type,
        value: formData.value,
        notes: formData.notes || undefined,
        recorded_at: formData.recorded_at,
      });

      // Reset form
      setFormData({
        record_type: "",
        value: "",
        notes: "",
        recorded_at: new Date().toISOString().slice(0, 16),
      });

      onSuccess();
      onClose();
    } catch (err: any) {
      console.error("Error creating health record:", err);
      setError(err.response?.data?.detail || "Có lỗi xảy ra khi lưu dữ liệu");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSelectedTypeConfig = () => {
    return healthTypes.find(t => t.type === formData.record_type);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-elderly-text">
            Ghi nhận sức khỏe mới
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Health Type Selection */}
          <div>
            <label className="block text-sm font-medium text-elderly-text mb-2">
              Loại chỉ số *
            </label>
            <select
              value={formData.record_type}
              onChange={(e) =>
                setFormData({ ...formData, record_type: e.target.value })
              }
              className="input w-full"
              required
            >
              <option value="">Chọn loại chỉ số</option>
              {healthTypes.map((type) => (
                <option key={type.type} value={type.type}>
                  {type.icon} {type.name} ({type.unit})
                </option>
              ))}
            </select>
          </div>

          {/* Value Input */}
          <div>
            <label className="block text-sm font-medium text-elderly-text mb-2">
              Giá trị *
            </label>
            <div className="relative">
              <input
                type="text"
                value={formData.value}
                onChange={(e) =>
                  setFormData({ ...formData, value: e.target.value })
                }
                placeholder={getSelectedTypeConfig()?.placeholder || "Nhập giá trị"}
                className="input w-full pr-16"
                required
              />
              {getSelectedTypeConfig() && (
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                  {getSelectedTypeConfig()?.unit}
                </span>
              )}
            </div>
            {getSelectedTypeConfig()?.type === "blood_pressure" && (
              <p className="text-xs text-gray-500 mt-1">
                Ví dụ: 120/80 (tâm thu/tâm trương)
              </p>
            )}
          </div>

          {/* Date Time */}
          <div>
            <label className="block text-sm font-medium text-elderly-text mb-2">
              Thời gian ghi nhận
            </label>
            <input
              type="datetime-local"
              value={formData.recorded_at}
              onChange={(e) =>
                setFormData({ ...formData, recorded_at: e.target.value })
              }
              className="input w-full"
            />
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-elderly-text mb-2">
              Ghi chú
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) =>
                setFormData({ ...formData, notes: e.target.value })
              }
              placeholder="Ghi chú thêm (tùy chọn)"
              className="input w-full h-20 resize-none"
            />
          </div>

          {/* Submit Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn btn-secondary flex-1"
              disabled={isSubmitting}
            >
              Hủy
            </button>
            <button
              type="submit"
              className="btn btn-primary flex-1"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Đang lưu..." : "Lưu"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddHealthRecordModal;
