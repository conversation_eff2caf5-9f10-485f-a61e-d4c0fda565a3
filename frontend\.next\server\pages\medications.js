/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/medications";
exports.ids = ["pages/medications"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightOnRectangleIcon: () => (/* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   HeartIcon: () => (/* reexport safe */ _HeartIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   UserCircleIcon: () => (/* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BellIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _HeartIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HeartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLEJhcnMzSWNvbixCZWxsSWNvbixDb2c2VG9vdGhJY29uLEhlYXJ0SWNvbixVc2VyQ2lyY2xlSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUNxRjtBQUNoQztBQUNGO0FBQ1U7QUFDUiIsInNvdXJjZXMiOlsid2VicGFjazovL2VsZGVybHktaGVhbHRoLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/ZjNmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiB9IGZyb20gXCIuL0Fycm93UmlnaHRPblJlY3RhbmdsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJzM0ljb24gfSBmcm9tIFwiLi9CYXJzM0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWxsSWNvbiB9IGZyb20gXCIuL0JlbGxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nNlRvb3RoSWNvbiB9IGZyb20gXCIuL0NvZzZUb290aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIZWFydEljb24gfSBmcm9tIFwiLi9IZWFydEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyQ2lyY2xlSWNvbiB9IGZyb20gXCIuL1VzZXJDaXJjbGVJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BeakerIcon: () => (/* reexport safe */ _BeakerIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalendarIcon: () => (/* reexport safe */ _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChatBubbleLeftRightIcon: () => (/* reexport safe */ _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   HeartIcon: () => (/* reexport safe */ _HeartIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   UserIcon: () => (/* reexport safe */ _UserIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _BeakerIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BeakerIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalendarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubbleLeftRightIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _HeartIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HeartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _UserIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UserIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CZWFrZXJJY29uLENhbGVuZGFySWNvbixDaGF0QnViYmxlTGVmdFJpZ2h0SWNvbixDb2c2VG9vdGhJY29uLEhlYXJ0SWNvbixIb21lSWNvbixVc2VySWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3VEO0FBQ0k7QUFDc0I7QUFDcEI7QUFDUjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz8xNGY4Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWFrZXJJY29uIH0gZnJvbSBcIi4vQmVha2VySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGVuZGFySWNvbiB9IGZyb20gXCIuL0NhbGVuZGFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXRCdWJibGVMZWZ0UmlnaHRJY29uIH0gZnJvbSBcIi4vQ2hhdEJ1YmJsZUxlZnRSaWdodEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2c2VG9vdGhJY29uIH0gZnJvbSBcIi4vQ29nNlRvb3RoSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhlYXJ0SWNvbiB9IGZyb20gXCIuL0hlYXJ0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWVJY29uIH0gZnJvbSBcIi4vSG9tZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VySWNvbiB9IGZyb20gXCIuL1VzZXJJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClockIcon: () => (/* reexport safe */ _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   PencilIcon: () => (/* reexport safe */ _PencilIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   PlusIcon: () => (/* reexport safe */ _PlusIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   TrashIcon: () => (/* reexport safe */ _TrashIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ClockIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _PencilIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PencilIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _PlusIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PlusIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _TrashIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TrashIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DbG9ja0ljb24sUGVuY2lsSWNvbixQbHVzSWNvbixUcmFzaEljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUNxRDtBQUNFO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGRlcmx5LWhlYWx0aC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzPzU2YjYiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENsb2NrSWNvbiB9IGZyb20gXCIuL0Nsb2NrSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBlbmNpbEljb24gfSBmcm9tIFwiLi9QZW5jaWxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGx1c0ljb24gfSBmcm9tIFwiLi9QbHVzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyYXNoSWNvbiB9IGZyb20gXCIuL1RyYXNoSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js":
/*!***********************************************************************************************************!*\
  !*** __barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Menu: () => (/* reexport safe */ D_CodeThue2025_SucKhoe_frontend_node_modules_headlessui_react_dist_components_menu_menu_js__WEBPACK_IMPORTED_MODULE_0__.Menu),\n/* harmony export */   Transition: () => (/* reexport safe */ D_CodeThue2025_SucKhoe_frontend_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__.Transition)\n/* harmony export */ });\n/* harmony import */ var D_CodeThue2025_SucKhoe_frontend_node_modules_headlessui_react_dist_components_menu_menu_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/menu/menu.js */ \"./node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var D_CodeThue2025_SucKhoe_frontend_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/transitions/transition.js */ \"./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NZW51LFRyYW5zaXRpb24hPSEuL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hlYWRsZXNzdWkuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQzZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9oZWFkbGVzc3VpLmVzbS5qcz9kZTdlIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgTWVudSB9IGZyb20gXCJEOlxcXFxDb2RlVGh1ZTIwMjVcXFxcU3VjS2hvZVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcQGhlYWRsZXNzdWlcXFxccmVhY3RcXFxcZGlzdFxcXFxjb21wb25lbnRzXFxcXG1lbnVcXFxcbWVudS5qc1wiXG5leHBvcnQgeyBUcmFuc2l0aW9uIH0gZnJvbSBcIkQ6XFxcXENvZGVUaHVlMjAyNVxcXFxTdWNLaG9lXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxAaGVhZGxlc3N1aVxcXFxyZWFjdFxcXFxkaXN0XFxcXGNvbXBvbmVudHNcXFxcdHJhbnNpdGlvbnNcXFxcdHJhbnNpdGlvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*****************************************************************************************************!*\
  !*** __barrel_optimize__?names=XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./XMarkIcon.js */ "./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js");



/***/ }),

/***/ "__barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!./node_modules/date-fns/esm/index.js":
/*!**********************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!./node_modules/date-fns/esm/index.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addDays: () => (/* reexport safe */ _addDays_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   differenceInYears: () => (/* reexport safe */ _differenceInYears_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   endOfDay: () => (/* reexport safe */ _endOfDay_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   format: () => (/* reexport safe */ _format_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   isValid: () => (/* reexport safe */ _isValid_index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   parseISO: () => (/* reexport safe */ _parseISO_index_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   startOfDay: () => (/* reexport safe */ _startOfDay_index_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _addDays_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./addDays/index.js */ \"./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _differenceInYears_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./differenceInYears/index.js */ \"./node_modules/date-fns/esm/differenceInYears/index.js\");\n/* harmony import */ var _endOfDay_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./endOfDay/index.js */ \"./node_modules/date-fns/esm/endOfDay/index.js\");\n/* harmony import */ var _format_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./format/index.js */ \"./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _isValid_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./isValid/index.js */ \"./node_modules/date-fns/esm/isValid/index.js\");\n/* harmony import */ var _parseISO_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./parseISO/index.js */ \"./node_modules/date-fns/esm/parseISO/index.js\");\n/* harmony import */ var _startOfDay_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./startOfDay/index.js */ \"./node_modules/date-fns/esm/startOfDay/index.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1hZGREYXlzLGRpZmZlcmVuY2VJblllYXJzLGVuZE9mRGF5LGZvcm1hdCxpc1ZhbGlkLHBhcnNlSVNPLHN0YXJ0T2ZEYXkhPSEuL25vZGVfbW9kdWxlcy9kYXRlLWZucy9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3VEO0FBQ29CO0FBQ2xCO0FBQ0o7QUFDRTtBQUNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvZXNtL2luZGV4LmpzP2JkN2EiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIGFkZERheXMgfSBmcm9tIFwiLi9hZGREYXlzL2luZGV4LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgZGlmZmVyZW5jZUluWWVhcnMgfSBmcm9tIFwiLi9kaWZmZXJlbmNlSW5ZZWFycy9pbmRleC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIGVuZE9mRGF5IH0gZnJvbSBcIi4vZW5kT2ZEYXkvaW5kZXguanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBmb3JtYXQgfSBmcm9tIFwiLi9mb3JtYXQvaW5kZXguanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBpc1ZhbGlkIH0gZnJvbSBcIi4vaXNWYWxpZC9pbmRleC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIHBhcnNlSVNPIH0gZnJvbSBcIi4vcGFyc2VJU08vaW5kZXguanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBzdGFydE9mRGF5IH0gZnJvbSBcIi4vc3RhcnRPZkRheS9pbmRleC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!./node_modules/date-fns/esm/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmedications&preferredRegion=&absolutePagePath=.%2Fpages%5Cmedications%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmedications&preferredRegion=&absolutePagePath=.%2Fpages%5Cmedications%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\medications\\index.tsx */ \"./pages/medications/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/medications\",\n        pathname: \"/medications\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_medications_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmedications&preferredRegion=&absolutePagePath=.%2Fpages%5Cmedications%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/Layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Footer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-elderly-border mt-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-elderly-text mb-4\",\n                                        children: \"Hệ thống SứcKhỏe\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 11,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elderly-text-light\",\n                                        children: \"Chăm s\\xf3c sức khỏe người cao tuổi với c\\xf4ng nghệ hiện đại v\\xe0 t\\xecnh y\\xeau thương.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-elderly-text mb-4\",\n                                        children: \"Li\\xean kết hữu \\xedch\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/about\",\n                                                    className: \"text-elderly-text-light hover:text-primary-600\",\n                                                    children: \"Về ch\\xfang t\\xf4i\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/privacy\",\n                                                    className: \"text-elderly-text-light hover:text-primary-600\",\n                                                    children: \"Ch\\xednh s\\xe1ch bảo mật\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 30,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/terms\",\n                                                    className: \"text-elderly-text-light hover:text-primary-600\",\n                                                    children: \"Điều khoản sử dụng\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-elderly-text mb-4\",\n                                        children: \"Hỗ trợ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-elderly-text-light\",\n                                                    children: \"Hotline: 1900-1234\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-elderly-text-light\",\n                                                    children: \"Email: <EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 pt-8 border-t border-elderly-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-elderly-text-light\",\n                            children: \"\\xa9 2024 Hệ thống hỗ trợ sức khỏe người cao tuổi. Tất cả quyền được bảo lưu.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout/Footer.tsx\n");

/***/ }),

/***/ "./components/Layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/Layout/Header.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * Header component for Elderly Health Support System\n */ \n\n\n\n\n\n\nconst Header = ({ onMenuClick })=>{\n    const { user, isLoading, logout } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [notificationsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3); // This would come from API\n    const navigation = [\n        {\n            name: \"Trang chủ\",\n            href: \"/\",\n            current: false\n        },\n        {\n            name: \"Sức khỏe\",\n            href: \"/health\",\n            current: false\n        },\n        {\n            name: \"Thuốc\",\n            href: \"/medications\",\n            current: false\n        },\n        {\n            name: \"Lịch hẹn\",\n            href: \"/schedules\",\n            current: false\n        },\n        {\n            name: \"Tư vấn AI\",\n            href: \"/chat\",\n            current: false\n        }\n    ];\n    const userNavigation = [\n        {\n            name: \"Hồ sơ c\\xe1 nh\\xe2n\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserCircleIcon\n        },\n        {\n            name: \"C\\xe0i đặt\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.Cog6ToothIcon\n        }\n    ];\n    const handleLogout = ()=>{\n        logout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-elderly-border sticky top-0 z-40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"lg:hidden -ml-2 mr-2 p-2 rounded-md text-elderly-text hover:bg-elderly-hover-bg focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                    onClick: onMenuClick,\n                                    \"aria-label\": \"Mở menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.Bars3Icon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.HeartIcon, {\n                                                className: \"h-8 w-8 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-xl font-bold text-elderly-text hidden sm:block\",\n                                                children: \"SứcKhỏe\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:ml-8 lg:flex lg:space-x-1\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"nav-link\", item.current ? \"nav-link-active\" : \"nav-link-inactive\"),\n                                            children: item.name\n                                        }, item.name, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"relative p-2 text-elderly-text hover:bg-elderly-hover-bg rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                        \"aria-label\": \"Th\\xf4ng b\\xe1o\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.BellIcon, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            notificationsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                children: notificationsCount > 9 ? \"9+\" : notificationsCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                                        as: \"div\",\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Button, {\n                                                className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-elderly-hover-bg focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserCircleIcon, {\n                                                            className: \"h-8 w-8 text-elderly-text-light\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden md:block text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-elderly-text\",\n                                                                children: user.full_name || \"Người d\\xf9ng\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-elderly-text-light\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Transition, {\n                                                enter: \"transition ease-out duration-100\",\n                                                enterFrom: \"transform opacity-0 scale-95\",\n                                                enterTo: \"transform opacity-100 scale-100\",\n                                                leave: \"transition ease-in duration-75\",\n                                                leaveFrom: \"transform opacity-100 scale-100\",\n                                                leaveTo: \"transform opacity-0 scale-95\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Items, {\n                                                    className: \"absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-1\",\n                                                        children: [\n                                                            userNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                            href: item.href,\n                                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center px-4 py-3 text-sm\", active ? \"bg-elderly-hover-bg text-elderly-text\" : \"text-elderly-text-light\"),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                                    className: \"mr-3 h-5 w-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                                    lineNumber: 144,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                item.name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                            lineNumber: 135,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                }, item.name, false, {\n                                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 27\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                                children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleLogout,\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center w-full px-4 py-3 text-sm text-left\", active ? \"bg-elderly-hover-bg text-elderly-text\" : \"text-elderly-text-light\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ArrowRightOnRectangleIcon, {\n                                                                                className: \"mr-3 h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                                lineNumber: 161,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            \"Đăng xuất\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                        lineNumber: 152,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/auth/login\",\n                                        className: \"text-elderly-text hover:text-primary-600 font-medium transition-colors\",\n                                        children: \"Đăng nhập\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/auth/register\",\n                                        className: \"btn btn-primary\",\n                                        children: \"Đăng k\\xfd\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden border-t border-elderly-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"px-4 py-2 space-y-1\",\n                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"block px-3 py-2 rounded-md text-base font-medium\", item.current ? \"bg-primary-100 text-primary-700\" : \"text-elderly-text hover:bg-elderly-hover-bg\"),\n                            children: item.name\n                        }, item.name, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout/Header.tsx\n");

/***/ }),

/***/ "./components/Layout/Layout.tsx":
/*!**************************************!*\
  !*** ./components/Layout/Layout.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"./components/Layout/Header.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Sidebar */ \"./components/Layout/Sidebar.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Footer */ \"./components/Layout/Footer.tsx\");\n/* harmony import */ var _UI_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../UI/LoadingSpinner */ \"./components/UI/LoadingSpinner.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_5__, _Sidebar__WEBPACK_IMPORTED_MODULE_6__, _UI_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_5__, _Sidebar__WEBPACK_IMPORTED_MODULE_6__, _UI_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * Main Layout component for Elderly Health Support System\n */ \n\n\n\n\n\n\n\n\nconst Layout = ({ children, title = \"Hệ thống hỗ trợ sức khỏe người cao tuổi\", description = \"Theo d\\xf5i v\\xe0 chăm s\\xf3c sức khỏe cho người cao tuổi\", showSidebar = true, className = \"\" })=>{\n    const { user, isLoading } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-elderly-bg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UI_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: \"/og-image.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: \"/og-image.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        as: \"style\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-elderly-bg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#main-content\",\n                        className: \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-lg z-50\",\n                        children: \"Chuyển đến nội dung ch\\xednh\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            showSidebar && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                                className: \"hidden lg:block w-64 bg-white shadow-sm border-r border-elderly-border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                id: \"main-content\",\n                                className: `flex-1 ${showSidebar && user ? \"lg:ml-0\" : \"\"} ${className}`,\n                                role: \"main\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-screen\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 5000,\n                            style: {\n                                fontSize: \"16px\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\"\n                            },\n                            success: {\n                                iconTheme: {\n                                    primary: \"#10b981\",\n                                    secondary: \"#ffffff\"\n                                }\n                            },\n                            error: {\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#ffffff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout/Layout.tsx\n");

/***/ }),

/***/ "./components/Layout/Sidebar.tsx":
/*!***************************************!*\
  !*** ./components/Layout/Sidebar.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Trang chủ\",\n        href: \"/\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.HomeIcon\n    },\n    {\n        name: \"Sức khỏe\",\n        href: \"/health\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.HeartIcon\n    },\n    {\n        name: \"Thuốc\",\n        href: \"/medications\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.BeakerIcon\n    },\n    {\n        name: \"Lịch hẹn\",\n        href: \"/schedules\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CalendarIcon\n    },\n    {\n        name: \"Tư vấn AI\",\n        href: \"/chat\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChatBubbleLeftRightIcon\n    },\n    {\n        name: \"Hồ sơ\",\n        href: \"/profile\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserIcon\n    },\n    {\n        name: \"C\\xe0i đặt\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.Cog6ToothIcon\n    }\n];\nconst Sidebar = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"flex-1 px-4 py-6 space-y-2\",\n            children: navigation.map((item)=>{\n                const isActive = router.pathname === item.href;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: item.href,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors\", isActive ? \"bg-primary-100 text-primary-700\" : \"text-elderly-text hover:bg-elderly-hover-bg hover:text-primary-600\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                            className: \"mr-3 h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 15\n                        }, undefined),\n                        item.name\n                    ]\n                }, item.name, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 13\n                }, undefined);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Sidebar.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout/Sidebar.tsx\n");

/***/ }),

/***/ "./components/Medications/AddMedicationModal.tsx":
/*!*******************************************************!*\
  !*** ./components/Medications/AddMedicationModal.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_api__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_api__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst AddMedicationModal = ({ isOpen, onClose, onSuccess, editingMedication })=>{\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        medication_name: \"\",\n        dosage: \"\",\n        frequency: \"\",\n        instructions: \"\",\n        start_date: \"\",\n        end_date: \"\",\n        is_active: true\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isEditing = !!editingMedication;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (editingMedication) {\n            setFormData({\n                medication_name: editingMedication.medication_name || \"\",\n                dosage: editingMedication.dosage || \"\",\n                frequency: editingMedication.frequency || \"\",\n                instructions: editingMedication.instructions || \"\",\n                start_date: editingMedication.start_date || \"\",\n                end_date: editingMedication.end_date || \"\",\n                is_active: editingMedication.is_active ?? true\n            });\n        } else {\n            setFormData({\n                medication_name: \"\",\n                dosage: \"\",\n                frequency: \"\",\n                instructions: \"\",\n                start_date: \"\",\n                end_date: \"\",\n                is_active: true\n            });\n        }\n    }, [\n        editingMedication\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.medication_name.trim()) {\n            setError(\"Vui l\\xf2ng nhập t\\xean thuốc\");\n            return;\n        }\n        try {\n            setIsSubmitting(true);\n            setError(null);\n            const submitData = {\n                ...formData,\n                start_date: formData.start_date || undefined,\n                end_date: formData.end_date || undefined\n            };\n            if (isEditing) {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_2__.medicationsApi.updateMedication(editingMedication.id, submitData);\n            } else {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_2__.medicationsApi.createMedication(submitData);\n            }\n            // Reset form\n            setFormData({\n                medication_name: \"\",\n                dosage: \"\",\n                frequency: \"\",\n                instructions: \"\",\n                start_date: \"\",\n                end_date: \"\",\n                is_active: true\n            });\n            onSuccess();\n            onClose();\n        } catch (err) {\n            console.error(\"Error saving medication:\", err);\n            setError(err.response?.data?.detail || \"C\\xf3 lỗi xảy ra khi lưu dữ liệu\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-elderly-text\",\n                            children: isEditing ? \"Chỉnh sửa thuốc\" : \"Th\\xeam thuốc mới\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-4\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 bg-red-50 border border-red-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-elderly-text mb-2\",\n                                    children: \"T\\xean thuốc *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.medication_name,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            medication_name: e.target.value\n                                        }),\n                                    placeholder: \"V\\xed dụ: Paracetamol\",\n                                    className: \"input w-full\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-elderly-text mb-2\",\n                                    children: \"Liều lượng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.dosage,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            dosage: e.target.value\n                                        }),\n                                    placeholder: \"V\\xed dụ: 500mg\",\n                                    className: \"input w-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-elderly-text mb-2\",\n                                    children: \"Tần suất sử dụng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: formData.frequency,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            frequency: e.target.value\n                                        }),\n                                    className: \"input w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Chọn tần suất\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"1 lần/ng\\xe0y\",\n                                            children: \"1 lần/ng\\xe0y\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"2 lần/ng\\xe0y\",\n                                            children: \"2 lần/ng\\xe0y\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"3 lần/ng\\xe0y\",\n                                            children: \"3 lần/ng\\xe0y\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"Khi cần thiết\",\n                                            children: \"Khi cần thiết\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"Kh\\xe1c\",\n                                            children: \"Kh\\xe1c\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-elderly-text mb-2\",\n                                    children: \"Hướng dẫn sử dụng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: formData.instructions,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            instructions: e.target.value\n                                        }),\n                                    placeholder: \"V\\xed dụ: Uống sau bữa ăn\",\n                                    className: \"input w-full h-20 resize-none\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-elderly-text mb-2\",\n                                            children: \"Ng\\xe0y bắt đầu\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.start_date,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    start_date: e.target.value\n                                                }),\n                                            className: \"input w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-elderly-text mb-2\",\n                                            children: \"Ng\\xe0y kết th\\xfac\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.end_date,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    end_date: e.target.value\n                                                }),\n                                            className: \"input w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, undefined),\n                        isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"is_active\",\n                                    checked: formData.is_active,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            is_active: e.target.checked\n                                        }),\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"is_active\",\n                                    className: \"text-sm text-elderly-text\",\n                                    children: \"Đang sử dụng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"btn btn-secondary flex-1\",\n                                    disabled: isSubmitting,\n                                    children: \"Hủy\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"btn btn-primary flex-1\",\n                                    disabled: isSubmitting,\n                                    children: isSubmitting ? \"Đang lưu...\" : isEditing ? \"Cập nhật\" : \"Th\\xeam\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Medications\\\\AddMedicationModal.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AddMedicationModal);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Medications/AddMedicationModal.tsx\n");

/***/ }),

/***/ "./components/UI/LoadingSpinner.tsx":
/*!******************************************!*\
  !*** ./components/UI/LoadingSpinner.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst LoadingSpinner = ({ size = \"md\", className })=>{\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        md: \"h-8 w-8\",\n        lg: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"loading-spinner\", sizeClasses[size], className)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\UI\\\\LoadingSpinner.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSpinner);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1VJL0xvYWRpbmdTcGlubmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQTBCO0FBQ087QUFPakMsTUFBTUUsaUJBQWdELENBQUMsRUFDckRDLE9BQU8sSUFBSSxFQUNYQyxTQUFTLEVBQ1Y7SUFDQyxNQUFNQyxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVdILDhDQUFFQSxDQUFDLG1CQUFtQkksV0FBVyxDQUFDRixLQUFLLEVBQUVDOzs7Ozs7QUFFN0Q7QUFFQSxpRUFBZUYsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2VsZGVybHktaGVhbHRoLWZyb250ZW5kLy4vY29tcG9uZW50cy9VSS9Mb2FkaW5nU3Bpbm5lci50c3g/ODA2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cbmludGVyZmFjZSBMb2FkaW5nU3Bpbm5lclByb3BzIHtcbiAgc2l6ZT86ICdzbScgfCAnbWQnIHwgJ2xnJztcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5jb25zdCBMb2FkaW5nU3Bpbm5lcjogUmVhY3QuRkM8TG9hZGluZ1NwaW5uZXJQcm9wcz4gPSAoeyBcbiAgc2l6ZSA9ICdtZCcsIFxuICBjbGFzc05hbWUgXG59KSA9PiB7XG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtOiAnaC00IHctNCcsXG4gICAgbWQ6ICdoLTggdy04JyxcbiAgICBsZzogJ2gtMTIgdy0xMidcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbignbG9hZGluZy1zcGlubmVyJywgc2l6ZUNsYXNzZXNbc2l6ZV0sIGNsYXNzTmFtZSl9IC8+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBMb2FkaW5nU3Bpbm5lcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiTG9hZGluZ1NwaW5uZXIiLCJzaXplIiwiY2xhc3NOYW1lIiwic2l6ZUNsYXNzZXMiLCJzbSIsIm1kIiwibGciLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/UI/LoadingSpinner.tsx\n");

/***/ }),

/***/ "./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiUtils: () => (/* binding */ apiUtils),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   chatApi: () => (/* binding */ chatApi),\n/* harmony export */   dashboardApi: () => (/* binding */ dashboardApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   healthApi: () => (/* binding */ healthApi),\n/* harmony export */   medicationsApi: () => (/* binding */ medicationsApi),\n/* harmony export */   schedulesApi: () => (/* binding */ schedulesApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__, js_cookie__WEBPACK_IMPORTED_MODULE_1__]);\n([axios__WEBPACK_IMPORTED_MODULE_0__, js_cookie__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * API client for Elderly Health Support System\n */ \n\n// API configuration\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 10000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use(async (config)=>{\n        try {\n            // Get token from cookies (simple auth)\n            if (false) {}\n        } catch (error) {\n            console.warn(\"Failed to get auth token:\", error);\n        }\n        return config;\n    }, (error)=>{\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>response, (error)=>{\n        // Log error for debugging\n        console.error(\"API Error:\", {\n            status: error.response?.status,\n            statusText: error.response?.statusText,\n            data: error.response?.data,\n            url: error.config?.url,\n            method: error.config?.method,\n            headers: error.config?.headers\n        });\n        if (error.response?.status === 401) {\n            // Redirect to login on unauthorized\n            console.warn(\"401 Unauthorized - redirecting to login\");\n        // TEMPORARILY DISABLED FOR DEBUGGING\n        // if (typeof window !== 'undefined') {\n        //   Cookies.remove('auth_token');\n        //   window.location.href = '/auth/login';\n        // }\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\n// API client instance\nconst apiClient = createApiClient();\n// Generic API request function\nconst apiRequest = async (method, url, data, config)=>{\n    try {\n        const response = await apiClient.request({\n            method,\n            url,\n            data,\n            ...config\n        });\n        return response.data;\n    } catch (error) {\n        console.error(`API ${method} ${url} error:`, error);\n        throw new Error(error.response?.data?.message || error.message || \"An unexpected error occurred\");\n    }\n};\n// Auth API\nconst authApi = {\n    // Login\n    login: async (email, password)=>{\n        const response = await apiRequest(\"POST\", \"/auth/login\", {\n            email,\n            password\n        });\n        // Store token in cookies\n        if (false) {}\n        return response;\n    },\n    // Register\n    register: async (userData)=>{\n        const response = await apiRequest(\"POST\", \"/auth/register\", userData);\n        // Store token in cookies\n        if (false) {}\n        return response;\n    },\n    // Logout\n    logout: ()=>{\n        if (false) {}\n    },\n    // Check if user is authenticated\n    isAuthenticated: ()=>{\n        if (false) {}\n        return false;\n    },\n    // Get current user info\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/auth/me\")\n};\n// User API\nconst userApi = {\n    // Get current user profile\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/users/me\"),\n    // Create user profile\n    createUser: (userData)=>apiRequest(\"POST\", \"/users\", userData),\n    // Update user profile\n    updateUser: (userData)=>apiRequest(\"PUT\", \"/users/me\", userData),\n    // Get health profile\n    getHealthProfile: ()=>apiRequest(\"GET\", \"/users/me/health-profile\"),\n    // Create health profile\n    createHealthProfile: (profileData)=>apiRequest(\"POST\", \"/users/me/health-profile\", profileData),\n    // Update health profile\n    updateHealthProfile: (profileData)=>apiRequest(\"PUT\", \"/users/me/health-profile\", profileData),\n    // Get user settings\n    getSettings: ()=>apiRequest(\"GET\", \"/users/me/settings\"),\n    // Create/update user setting\n    updateSetting: (key, value)=>apiRequest(\"POST\", \"/users/me/settings\", {\n            setting_key: key,\n            setting_value: value\n        })\n};\n// Health Records API\nconst healthApi = {\n    // Get health records\n    getRecords: (params)=>apiRequest(\"GET\", \"/health/records\", undefined, {\n            params\n        }),\n    // Create health record\n    createRecord: (recordData)=>apiRequest(\"POST\", \"/health/records\", recordData),\n    // Get specific health record\n    getRecord: (recordId)=>apiRequest(\"GET\", `/health/records/${recordId}`),\n    // Delete health record\n    deleteRecord: (recordId)=>apiRequest(\"DELETE\", `/health/records/${recordId}`),\n    // Get health statistics\n    getStats: (recordType)=>apiRequest(\"GET\", `/health/stats/${recordType}`)\n};\n// Medications API\nconst medicationsApi = {\n    // Get medications\n    getMedications: (activeOnly = true)=>apiRequest(\"GET\", \"/medications\", undefined, {\n            params: {\n                active_only: activeOnly\n            }\n        }),\n    // Create medication\n    createMedication: (medicationData)=>apiRequest(\"POST\", \"/medications\", medicationData),\n    // Get specific medication\n    getMedication: (medicationId)=>apiRequest(\"GET\", `/medications/${medicationId}`),\n    // Update medication\n    updateMedication: (medicationId, medicationData)=>apiRequest(\"PUT\", `/medications/${medicationId}`, medicationData),\n    // Delete medication\n    deleteMedication: (medicationId)=>apiRequest(\"DELETE\", `/medications/${medicationId}`)\n};\n// Schedules API\nconst schedulesApi = {\n    // Get schedules\n    getSchedules: (params)=>apiRequest(\"GET\", \"/schedules\", undefined, {\n            params\n        }),\n    // Create schedule\n    createSchedule: (scheduleData)=>apiRequest(\"POST\", \"/schedules\", scheduleData),\n    // Get today's schedules\n    getTodaySchedules: ()=>apiRequest(\"GET\", \"/schedules/today\"),\n    // Get specific schedule\n    getSchedule: (scheduleId)=>apiRequest(\"GET\", `/schedules/${scheduleId}`),\n    // Update schedule\n    updateSchedule: (scheduleId, scheduleData)=>apiRequest(\"PUT\", `/schedules/${scheduleId}`, scheduleData),\n    // Delete schedule\n    deleteSchedule: (scheduleId)=>apiRequest(\"DELETE\", `/schedules/${scheduleId}`),\n    // Get reminders\n    getReminders: (params)=>apiRequest(\"GET\", \"/schedules/reminders\", undefined, {\n            params\n        }),\n    // Mark reminder as read\n    markReminderRead: (reminderId)=>apiRequest(\"PUT\", `/schedules/reminders/${reminderId}/read`)\n};\n// Chat API\nconst chatApi = {\n    // Create chat session\n    createSession: ()=>apiRequest(\"POST\", \"/chat/sessions\"),\n    // Get active session\n    getActiveSession: ()=>apiRequest(\"GET\", \"/chat/sessions/active\"),\n    // Send message\n    sendMessage: (sessionId, content)=>apiRequest(\"POST\", `/chat/sessions/${sessionId}/messages`, {\n            content\n        }),\n    // Get chat history\n    getChatHistory: (sessionId)=>apiRequest(\"GET\", `/chat/sessions/${sessionId}/messages`),\n    // End session\n    endSession: (sessionId)=>apiRequest(\"PUT\", `/chat/sessions/${sessionId}/end`)\n};\n// Dashboard API\nconst dashboardApi = {\n    // Get dashboard stats\n    getStats: ()=>apiRequest(\"GET\", \"/dashboard/stats\"),\n    // Get recent activity\n    getRecentActivity: (limit = 10)=>apiRequest(\"GET\", \"/dashboard/activity\", undefined, {\n            params: {\n                limit\n            }\n        }),\n    // Get health summary\n    getHealthSummary: ()=>apiRequest(\"GET\", \"/dashboard/health-summary\")\n};\n// Utility functions\nconst apiUtils = {\n    // Check API health\n    checkHealth: ()=>apiRequest(\"GET\", \"/health\"),\n    // Get API info\n    getInfo: ()=>apiRequest(\"GET\", \"/info\"),\n    // Upload file (if needed)\n    uploadFile: async (file, endpoint)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return apiRequest(\"POST\", endpoint, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n    }\n};\n// Export default API client\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api.ts\n");

/***/ }),

/***/ "./lib/auth.tsx":
/*!**********************!*\
  !*** ./lib/auth.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useUser: () => (/* binding */ useUser),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_2__, axios__WEBPACK_IMPORTED_MODULE_4__]);\n([js_cookie__WEBPACK_IMPORTED_MODULE_2__, axios__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * Simple authentication context and hooks\n */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"auth_token\");\n            if (savedToken) {\n                setToken(savedToken);\n                try {\n                    // Verify token and get user info\n                    const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(`${API_BASE_URL}/auth/me`, {\n                        headers: {\n                            Authorization: `Bearer ${savedToken}`\n                        }\n                    });\n                    setUser(response.data);\n                } catch (error) {\n                    console.error(\"Token verification failed:\", error);\n                    // Remove invalid token\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n                    setToken(null);\n                }\n            }\n            setIsLoading(false);\n        };\n        initAuth();\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(`${API_BASE_URL}/auth/login`, {\n                email,\n                password\n            });\n            const { access_token, user: userData } = response.data;\n            // Save token to cookie\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", access_token, {\n                expires: 1\n            }); // 1 day\n            setToken(access_token);\n            setUser(userData);\n            // Redirect to dashboard\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw new Error(error.response?.data?.detail || \"Login failed. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (email, password, full_name, phone)=>{\n        try {\n            setIsLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(`${API_BASE_URL}/auth/register`, {\n                email,\n                password,\n                full_name,\n                phone\n            });\n            const { access_token, user: userData } = response.data;\n            // Save token to cookie\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", access_token, {\n                expires: 1\n            }); // 1 day\n            setToken(access_token);\n            setUser(userData);\n            // Redirect to dashboard\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Registration failed:\", error);\n            throw new Error(error.response?.data?.detail || \"Registration failed. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        // Remove token from cookie\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n        setToken(null);\n        setUser(null);\n        // Redirect to home\n        router.push(\"/\");\n    };\n    const value = {\n        user,\n        isLoading,\n        login,\n        register,\n        logout,\n        token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n        lineNumber: 159,\n        columnNumber: 10\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// HOC for protected pages\nconst withAuth = (Component)=>{\n    return function AuthenticatedComponent(props) {\n        const { user, isLoading } = useAuth();\n        const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!isLoading && !user) {\n                router.push(\"/auth/login\");\n            }\n        }, [\n            user,\n            isLoading,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            return null; // Will redirect\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n            lineNumber: 196,\n            columnNumber: 12\n        }, this);\n    };\n};\n// Hook for checking if user is authenticated\nconst useUser = ()=>{\n    const { user, isLoading } = useAuth();\n    return {\n        user,\n        isLoading,\n        error: null\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/auth.tsx\n");

/***/ }),

/***/ "./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateAge: () => (/* binding */ calculateAge),\n/* harmony export */   calculateBMI: () => (/* binding */ calculateBMI),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   downloadJSON: () => (/* binding */ downloadJSON),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getBMICategory: () => (/* binding */ getBMICategory),\n/* harmony export */   getDateRange: () => (/* binding */ getDateRange),\n/* harmony export */   getHealthStatus: () => (/* binding */ getHealthStatus),\n/* harmony export */   getHealthStatusColor: () => (/* binding */ getHealthStatusColor),\n/* harmony export */   getHealthStatusMessage: () => (/* binding */ getHealthStatusMessage),\n/* harmony export */   getRecordTypeDisplayName: () => (/* binding */ getRecordTypeDisplayName),\n/* harmony export */   getRecordTypeUnit: () => (/* binding */ getRecordTypeUnit),\n/* harmony export */   isElderly: () => (/* binding */ isElderly),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhoneNumber: () => (/* binding */ isValidPhoneNumber),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\n/* harmony import */ var _barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!date-fns */ \"__barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!./node_modules/date-fns/esm/index.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! date-fns/locale */ \"date-fns/locale\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(date_fns_locale__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * Utility functions for Elderly Health Support System\n */ \n\n\n\n/**\n * Combine class names with Tailwind CSS merge\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format date for display\n */ function formatDate(date, formatStr = \"dd/MM/yyyy\") {\n    try {\n        const dateObj = typeof date === \"string\" ? (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isValid)(dateObj)) return \"Ng\\xe0y kh\\xf4ng hợp lệ\";\n        return (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)(dateObj, formatStr, {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_2__.vi\n        });\n    } catch (error) {\n        console.error(\"Error formatting date:\", error);\n        return \"Ng\\xe0y kh\\xf4ng hợp lệ\";\n    }\n}\n/**\n * Format datetime for display\n */ function formatDateTime(date) {\n    return formatDate(date, \"dd/MM/yyyy HH:mm\");\n}\n/**\n * Format time for display\n */ function formatTime(date) {\n    return formatDate(date, \"HH:mm\");\n}\n/**\n * Calculate age from date of birth\n */ function calculateAge(dateOfBirth) {\n    try {\n        const birthDate = typeof dateOfBirth === \"string\" ? (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.parseISO)(dateOfBirth) : dateOfBirth;\n        if (!(0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isValid)(birthDate)) return 0;\n        return (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.differenceInYears)(new Date(), birthDate);\n    } catch (error) {\n        console.error(\"Error calculating age:\", error);\n        return 0;\n    }\n}\n/**\n * Format phone number for display\n */ function formatPhoneNumber(phone) {\n    if (!phone) return \"\";\n    // Remove all non-digits\n    const cleaned = phone.replace(/\\D/g, \"\");\n    // Format Vietnamese phone numbers\n    if (cleaned.length === 10 && cleaned.startsWith(\"0\")) {\n        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;\n    }\n    if (cleaned.length === 11 && cleaned.startsWith(\"84\")) {\n        return `+84 ${cleaned.slice(2, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`;\n    }\n    return phone;\n}\n/**\n * Validate email address\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate Vietnamese phone number\n */ function isValidPhoneNumber(phone) {\n    const phoneRegex = /^(\\+84|84|0)(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-6|8|9]|9[0-4|6-9])[0-9]{7}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n}\n/**\n * Get health status based on record type and value\n */ function getHealthStatus(record) {\n    if (!record) return \"unknown\";\n    switch(record.record_type){\n        case \"blood_pressure\":\n            if (!record.systolic_pressure || !record.diastolic_pressure) return \"unknown\";\n            if (record.systolic_pressure >= 180 || record.diastolic_pressure >= 110) return \"danger\";\n            if (record.systolic_pressure >= 140 || record.diastolic_pressure >= 90) return \"warning\";\n            if (record.systolic_pressure >= 120 || record.diastolic_pressure >= 80) return \"warning\";\n            return \"normal\";\n        case \"heart_rate\":\n            if (!record.heart_rate) return \"unknown\";\n            if (record.heart_rate < 50 || record.heart_rate > 120) return \"danger\";\n            if (record.heart_rate < 60 || record.heart_rate > 100) return \"warning\";\n            return \"normal\";\n        case \"blood_sugar\":\n            if (!record.blood_sugar) return \"unknown\";\n            if (record.blood_sugar < 50 || record.blood_sugar > 300) return \"danger\";\n            if (record.blood_sugar < 70 || record.blood_sugar > 180) return \"warning\";\n            return \"normal\";\n        case \"temperature\":\n            if (!record.temperature) return \"unknown\";\n            if (record.temperature < 35 || record.temperature > 39) return \"danger\";\n            if (record.temperature < 36 || record.temperature > 37.5) return \"warning\";\n            return \"normal\";\n        case \"weight\":\n            return \"normal\"; // Weight doesn't have universal normal ranges\n        default:\n            return \"unknown\";\n    }\n}\n/**\n * Get health status color\n */ function getHealthStatusColor(status) {\n    switch(status){\n        case \"normal\":\n            return \"text-green-600 bg-green-100\";\n        case \"warning\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"danger\":\n            return \"text-red-600 bg-red-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\n/**\n * Get health status message\n */ function getHealthStatusMessage(status, recordType) {\n    const messages = {\n        normal: {\n            blood_pressure: \"Huyết \\xe1p b\\xecnh thường\",\n            heart_rate: \"Nhịp tim b\\xecnh thường\",\n            blood_sugar: \"Đường huyết b\\xecnh thường\",\n            weight: \"C\\xe2n nặng ổn định\",\n            temperature: \"Nhiệt độ b\\xecnh thường\"\n        },\n        warning: {\n            blood_pressure: \"Huyết \\xe1p hơi cao, cần theo d\\xf5i\",\n            heart_rate: \"Nhịp tim bất thường, cần ch\\xfa \\xfd\",\n            blood_sugar: \"Đường huyết cao, cần kiểm so\\xe1t\",\n            weight: \"C\\xe2n nặng thay đổi\",\n            temperature: \"Nhiệt độ hơi cao\"\n        },\n        danger: {\n            blood_pressure: \"Huyết \\xe1p rất cao, cần kh\\xe1m ngay\",\n            heart_rate: \"Nhịp tim bất thường nghi\\xeam trọng\",\n            blood_sugar: \"Đường huyết nguy hiểm\",\n            weight: \"C\\xe2n nặng thay đổi đ\\xe1ng lo\",\n            temperature: \"Sốt cao, cần chăm s\\xf3c y tế\"\n        },\n        unknown: {\n            blood_pressure: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\",\n            heart_rate: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\",\n            blood_sugar: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\",\n            weight: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\",\n            temperature: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\"\n        }\n    };\n    return messages[status][recordType] || \"Kh\\xf4ng x\\xe1c định\";\n}\n/**\n * Get record type display name\n */ function getRecordTypeDisplayName(recordType) {\n    const displayNames = {\n        blood_pressure: \"Huyết \\xe1p\",\n        heart_rate: \"Nhịp tim\",\n        blood_sugar: \"Đường huyết\",\n        weight: \"C\\xe2n nặng\",\n        temperature: \"Nhiệt độ\"\n    };\n    return displayNames[recordType] || recordType;\n}\n/**\n * Get record type unit\n */ function getRecordTypeUnit(recordType) {\n    const units = {\n        blood_pressure: \"mmHg\",\n        heart_rate: \"bpm\",\n        blood_sugar: \"mg/dL\",\n        weight: \"kg\",\n        temperature: \"\\xb0C\"\n    };\n    return units[recordType] || \"\";\n}\n/**\n * Format number with locale\n */ function formatNumber(value, decimals = 1) {\n    return new Intl.NumberFormat(\"vi-VN\", {\n        minimumFractionDigits: decimals,\n        maximumFractionDigits: decimals\n    }).format(value);\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Throttle function\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * Generate random ID\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (error) {\n        console.error(\"Failed to copy to clipboard:\", error);\n        return false;\n    }\n}\n/**\n * Download data as JSON file\n */ function downloadJSON(data, filename) {\n    const blob = new Blob([\n        JSON.stringify(data, null, 2)\n    ], {\n        type: \"application/json\"\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = `${filename}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n}\n/**\n * Get date range for filtering\n */ function getDateRange(period) {\n    const now = new Date();\n    const today = (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.startOfDay)(now);\n    switch(period){\n        case \"today\":\n            return {\n                start: today,\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n        case \"week\":\n            return {\n                start: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.addDays)(today, -7),\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n        case \"month\":\n            return {\n                start: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.addDays)(today, -30),\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n        case \"year\":\n            return {\n                start: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.addDays)(today, -365),\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n        default:\n            return {\n                start: today,\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n    }\n}\n/**\n * Check if user is elderly (65+)\n */ function isElderly(age) {\n    return age >= 65;\n}\n/**\n * Get BMI category\n */ function getBMICategory(bmi) {\n    if (bmi < 18.5) return \"Thiếu c\\xe2n\";\n    if (bmi < 25) return \"B\\xecnh thường\";\n    if (bmi < 30) return \"Thừa c\\xe2n\";\n    return \"B\\xe9o ph\\xec\";\n}\n/**\n * Calculate BMI\n */ function calculateBMI(weight, height) {\n    if (!weight || !height || height === 0) return 0;\n    const heightInMeters = height / 100;\n    return weight / (heightInMeters * heightInMeters);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/utils.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query/devtools */ \"react-query/devtools\");\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_query_devtools__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/**\n * Next.js App component for Elderly Health Support System\n */ \n\n\n\n\n\n// Create a client\nconst createQueryClient = ()=>new react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n        defaultOptions: {\n            queries: {\n                retry: 1,\n                refetchOnWindowFocus: false,\n                staleTime: 5 * 60 * 1000,\n                cacheTime: 10 * 60 * 1000\n            },\n            mutations: {\n                retry: 1\n            }\n        }\n    });\nfunction App({ Component, pageProps }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(()=>createQueryClient());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider, {\n            client: queryClient,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query_devtools__WEBPACK_IMPORTED_MODULE_3__.ReactQueryDevtools, {\n                    initialIsOpen: false\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"vi\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2RDtBQUU5QyxTQUFTSTtJQUN0QixxQkFDRSw4REFBQ0osK0NBQUlBO1FBQUNLLE1BQUs7OzBCQUNULDhEQUFDSiwrQ0FBSUE7O2tDQUNILDhEQUFDSzt3QkFBS0MsU0FBUTs7Ozs7O2tDQUNkLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7O2tDQUN0Qiw4REFBQ0Y7d0JBQ0NFLE1BQUs7d0JBQ0xELEtBQUk7Ozs7Ozs7Ozs7OzswQkFHUiw4REFBQ0U7O2tDQUNDLDhEQUFDVCwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9wYWdlcy9fZG9jdW1lbnQudHN4P2QzN2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSHRtbCwgSGVhZCwgTWFpbiwgTmV4dFNjcmlwdCB9IGZyb20gJ25leHQvZG9jdW1lbnQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEb2N1bWVudCgpIHtcbiAgcmV0dXJuIChcbiAgICA8SHRtbCBsYW5nPVwidmlcIj5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8bWV0YSBjaGFyU2V0PVwidXRmLThcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICAgIDxsaW5rXG4gICAgICAgICAgaHJlZj1cImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb20vY3NzMj9mYW1pbHk9SW50ZXI6d2dodEAzMDA7NDAwOzUwMDs2MDA7NzAwJmRpc3BsYXk9c3dhcFwiXG4gICAgICAgICAgcmVsPVwic3R5bGVzaGVldFwiXG4gICAgICAgIC8+XG4gICAgICA8L0hlYWQ+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPE1haW4gLz5cbiAgICAgICAgPE5leHRTY3JpcHQgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L0h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSHRtbCIsIkhlYWQiLCJNYWluIiwiTmV4dFNjcmlwdCIsIkRvY3VtZW50IiwibGFuZyIsIm1ldGEiLCJjaGFyU2V0IiwibGluayIsInJlbCIsImhyZWYiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/medications/index.tsx":
/*!*************************************!*\
  !*** ./pages/medications/index.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _components_Medications_AddMedicationModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Medications/AddMedicationModal */ \"./components/Medications/AddMedicationModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__, _lib_api__WEBPACK_IMPORTED_MODULE_4__, _components_Medications_AddMedicationModal__WEBPACK_IMPORTED_MODULE_5__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__, _lib_api__WEBPACK_IMPORTED_MODULE_4__, _components_Medications_AddMedicationModal__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst MedicationsPage = ()=>{\n    const [medications, setMedications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMed, setEditingMed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInactive, setShowInactive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadMedications();\n    }, [\n        showInactive\n    ]);\n    const loadMedications = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.medicationsApi.getMedications(!showInactive);\n            setMedications(data);\n        } catch (err) {\n            console.error(\"Error loading medications:\", err);\n            setError(\"Kh\\xf4ng thể tải danh s\\xe1ch thuốc\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Bạn c\\xf3 chắc chắn muốn x\\xf3a thuốc n\\xe0y?\")) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_4__.medicationsApi.deleteMedication(id);\n            await loadMedications();\n        } catch (err) {\n            console.error(\"Error deleting medication:\", err);\n            setError(\"Kh\\xf4ng thể x\\xf3a thuốc\");\n        }\n    };\n    const handleAdd = ()=>{\n        setEditingMed(null);\n        setShowAddForm(true);\n    };\n    const handleEdit = (medication)=>{\n        setEditingMed(medication);\n        setShowAddForm(true);\n    };\n    const getStatusColor = (medication)=>{\n        if (!medication.is_active) {\n            return \"bg-gray-100 text-gray-800\";\n        }\n        if (medication.end_date) {\n            const endDate = new Date(medication.end_date);\n            const today = new Date();\n            const daysLeft = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            if (daysLeft <= 0) {\n                return \"bg-red-100 text-red-800\";\n            } else if (daysLeft <= 7) {\n                return \"bg-yellow-100 text-yellow-800\";\n            }\n        }\n        return \"bg-green-100 text-green-800\";\n    };\n    const getStatusText = (medication)=>{\n        if (!medication.is_active) {\n            return \"Đ\\xe3 ngừng\";\n        }\n        if (medication.end_date) {\n            const endDate = new Date(medication.end_date);\n            const today = new Date();\n            const daysLeft = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            if (daysLeft <= 0) {\n                return \"Hết hạn\";\n            } else if (daysLeft <= 7) {\n                return `Còn ${daysLeft} ngày`;\n            }\n        }\n        return \"Đang d\\xf9ng\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"Quản l\\xfd thuốc\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Quản l\\xfd thuốc\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-elderly-text\",\n                                children: \"Quản l\\xfd thuốc\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAdd,\n                                    className: \"btn btn-primary flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PlusIcon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Th\\xeam thuốc mới\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadMedications,\n                                className: \"mt-2 text-red-600 hover:text-red-800 text-sm underline\",\n                                children: \"Thử lại\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: medications.length > 0 ? medications.map((medication)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-elderly-text\",\n                                                            children: medication.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `px-3 py-1 rounded-full text-sm ${getStatusColor(medication)}`,\n                                                            children: getStatusText(medication)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-elderly-text-light\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Liều d\\xf9ng:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        medication.dosage\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-elderly-text-light\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Tần suất:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        medication.frequency\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-elderly-text-light\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Bắt đầu:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                            lineNumber: 198,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        new Date(medication.start_date).toLocaleDateString(\"vi-VN\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                    lineNumber: 197,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                medication.end_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-elderly-text-light\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Kết th\\xfac:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        new Date(medication.end_date).toLocaleDateString(\"vi-VN\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                medication.instructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ClockIcon, {\n                                                                className: \"h-4 w-4 inline mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Hướng dẫn:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            \" \",\n                                                            medication.instructions\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2 ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEdit(medication),\n                                                    className: \"p-2 text-blue-600 hover:bg-blue-50 rounded-lg\",\n                                                    title: \"Chỉnh sửa\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PencilIcon, {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDelete(medication.id),\n                                                    className: \"p-2 text-red-600 hover:bg-red-50 rounded-lg\",\n                                                    title: \"X\\xf3a\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.TrashIcon, {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, medication.id, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDC8A\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-elderly-text mb-2\",\n                                    children: showInactive ? \"Kh\\xf4ng c\\xf3 thuốc đ\\xe3 ngừng\" : \"Chưa c\\xf3 thuốc n\\xe0o\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light mb-6\",\n                                    children: showInactive ? \"Bạn chưa c\\xf3 thuốc n\\xe0o đ\\xe3 ngừng sử dụng\" : \"H\\xe3y th\\xeam thuốc đầu ti\\xean để bắt đầu theo d\\xf5i\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, undefined),\n                                !showInactive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAdd,\n                                    className: \"btn btn-primary\",\n                                    children: \"Th\\xeam thuốc đầu ti\\xean\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    medications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-green-600\",\n                                        children: medications.filter((m)=>m.is_active).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elderly-text-light\",\n                                        children: \"Thuốc đang d\\xf9ng\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-yellow-600\",\n                                        children: medications.filter((m)=>{\n                                            if (!m.end_date || !m.is_active) return false;\n                                            const daysLeft = Math.ceil((new Date(m.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));\n                                            return daysLeft <= 7 && daysLeft > 0;\n                                        }).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elderly-text-light\",\n                                        children: \"Sắp hết hạn\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-gray-600\",\n                                        children: medications.filter((m)=>!m.is_active).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elderly-text-light\",\n                                        children: \"Đ\\xe3 ngừng\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Medications_AddMedicationModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAddForm,\n                onClose: ()=>{\n                    setShowAddForm(false);\n                    setEditingMed(null);\n                },\n                onSuccess: loadMedications,\n                editingMedication: editingMed\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(MedicationsPage));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/medications/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "date-fns/locale":
/*!**********************************!*\
  !*** external "date-fns/locale" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("date-fns/locale");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-query":
/*!******************************!*\
  !*** external "react-query" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ "react-query/devtools":
/*!***************************************!*\
  !*** external "react-query/devtools" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query/devtools");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "js-cookie":
/*!****************************!*\
  !*** external "js-cookie" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/date-fns","vendor-chunks/@headlessui","vendor-chunks/@heroicons","vendor-chunks/@babel"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmedications&preferredRegion=&absolutePagePath=.%2Fpages%5Cmedications%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();