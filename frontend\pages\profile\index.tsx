import React from 'react';
import { withPageAuthRequired } from '@auth0/nextjs-auth0/client';
import { useUser } from '@auth0/nextjs-auth0/client';
import Layout from '@/components/Layout/Layout';
import { UserIcon, HeartIcon, PhoneIcon, MapPinIcon } from '@heroicons/react/24/outline';

const ProfilePage: React.FC = () => {
  const { user } = useUser();

  return (
    <Layout title="Hồ sơ cá nhân">
      <div className="p-6">
        <h1 className="text-3xl font-bold text-elderly-text mb-6">
          <PERSON><PERSON> sơ cá nhân
        </h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Personal Information */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <UserIcon className="h-5 w-5 mr-2 text-primary-600" />
              Thông tin cá nhân
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Họ và tên
                </label>
                <input
                  type="text"
                  defaultValue={user?.name || ''}
                  className="form-input"
                  placeholder="Nhập họ và tên"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Email
                </label>
                <input
                  type="email"
                  defaultValue={user?.email || ''}
                  className="form-input"
                  disabled
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Số điện thoại
                </label>
                <input
                  type="tel"
                  className="form-input"
                  placeholder="Nhập số điện thoại"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Ngày sinh
                </label>
                <input
                  type="date"
                  className="form-input"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Giới tính
                </label>
                <select className="form-select">
                  <option value="">Chọn giới tính</option>
                  <option value="male">Nam</option>
                  <option value="female">Nữ</option>
                  <option value="other">Khác</option>
                </select>
              </div>
              
              <button className="btn btn-primary w-full">
                Cập nhật thông tin
              </button>
            </div>
          </div>

          {/* Health Profile */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <HeartIcon className="h-5 w-5 mr-2 text-primary-600" />
              Hồ sơ sức khỏe
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Chiều cao (cm)
                </label>
                <input
                  type="number"
                  className="form-input"
                  placeholder="Nhập chiều cao"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Nhóm máu
                </label>
                <select className="form-select">
                  <option value="">Chọn nhóm máu</option>
                  <option value="A+">A+</option>
                  <option value="A-">A-</option>
                  <option value="B+">B+</option>
                  <option value="B-">B-</option>
                  <option value="AB+">AB+</option>
                  <option value="AB-">AB-</option>
                  <option value="O+">O+</option>
                  <option value="O-">O-</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Bệnh nền
                </label>
                <textarea
                  className="form-textarea"
                  rows={3}
                  placeholder="Mô tả các bệnh nền (nếu có)"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Dị ứng
                </label>
                <textarea
                  className="form-textarea"
                  rows={2}
                  placeholder="Mô tả các dị ứng (nếu có)"
                />
              </div>
              
              <button className="btn btn-primary w-full">
                Cập nhật hồ sơ sức khỏe
              </button>
            </div>
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="mt-6">
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <PhoneIcon className="h-5 w-5 mr-2 text-primary-600" />
              Liên hệ khẩn cấp
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Tên người liên hệ
                </label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="Nhập tên người liên hệ"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Số điện thoại
                </label>
                <input
                  type="tel"
                  className="form-input"
                  placeholder="Nhập số điện thoại"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-elderly-text mb-1">
                  Địa chỉ
                </label>
                <textarea
                  className="form-textarea"
                  rows={2}
                  placeholder="Nhập địa chỉ"
                />
              </div>
              
              <div className="md:col-span-2">
                <button className="btn btn-primary">
                  Cập nhật thông tin liên hệ
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default withPageAuthRequired(ProfilePage);
