import React, { useState } from 'react';
import { withPageAuthRequired } from '@auth0/nextjs-auth0/client';
import Layout from '@/components/Layout/Layout';
import { 
  Cog6ToothIcon, 
  BellIcon, 
  EyeIcon, 
  ShieldCheckIcon,
  LanguageIcon 
} from '@heroicons/react/24/outline';

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: true,
      sms: false,
    },
    display: {
      fontSize: 'large',
      theme: 'light',
      language: 'vi',
    },
    privacy: {
      shareData: false,
      analytics: true,
    },
    reminders: {
      advanceMinutes: 30,
      sound: true,
    },
  });

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  return (
    <Layout title="Cài đặt">
      <div className="p-6">
        <h1 className="text-3xl font-bold text-elderly-text mb-6">
          Cài đặt hệ thống
        </h1>
        
        <div className="space-y-6">
          {/* Notification Settings */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <BellIcon className="h-5 w-5 mr-2 text-primary-600" />
              Thông báo
            </h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-elderly-text">Thông báo email</h3>
                  <p className="text-elderly-text-light text-sm">Nhận thông báo qua email</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.notifications.email}
                    onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-elderly-text">Thông báo push</h3>
                  <p className="text-elderly-text-light text-sm">Nhận thông báo trên trình duyệt</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.notifications.push}
                    onChange={(e) => handleSettingChange('notifications', 'push', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-elderly-text">Thông báo SMS</h3>
                  <p className="text-elderly-text-light text-sm">Nhận thông báo qua tin nhắn</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.notifications.sms}
                    onChange={(e) => handleSettingChange('notifications', 'sms', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
            </div>
          </div>

          {/* Display Settings */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <EyeIcon className="h-5 w-5 mr-2 text-primary-600" />
              Hiển thị
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-2">
                  Kích thước chữ
                </label>
                <select
                  value={settings.display.fontSize}
                  onChange={(e) => handleSettingChange('display', 'fontSize', e.target.value)}
                  className="form-select"
                >
                  <option value="small">Nhỏ</option>
                  <option value="medium">Vừa</option>
                  <option value="large">Lớn</option>
                  <option value="extra-large">Rất lớn</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-2">
                  Giao diện
                </label>
                <select
                  value={settings.display.theme}
                  onChange={(e) => handleSettingChange('display', 'theme', e.target.value)}
                  className="form-select"
                >
                  <option value="light">Sáng</option>
                  <option value="dark">Tối</option>
                  <option value="auto">Tự động</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-2">
                  Ngôn ngữ
                </label>
                <select
                  value={settings.display.language}
                  onChange={(e) => handleSettingChange('display', 'language', e.target.value)}
                  className="form-select"
                >
                  <option value="vi">Tiếng Việt</option>
                  <option value="en">English</option>
                </select>
              </div>
            </div>
          </div>

          {/* Reminder Settings */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Cog6ToothIcon className="h-5 w-5 mr-2 text-primary-600" />
              Nhắc nhở
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-elderly-text mb-2">
                  Thời gian nhắc trước (phút)
                </label>
                <select
                  value={settings.reminders.advanceMinutes}
                  onChange={(e) => handleSettingChange('reminders', 'advanceMinutes', parseInt(e.target.value))}
                  className="form-select"
                >
                  <option value={15}>15 phút</option>
                  <option value={30}>30 phút</option>
                  <option value={60}>1 giờ</option>
                  <option value={120}>2 giờ</option>
                </select>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-elderly-text">Âm thanh nhắc nhở</h3>
                  <p className="text-elderly-text-light text-sm">Phát âm thanh khi có nhắc nhở</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.reminders.sound}
                    onChange={(e) => handleSettingChange('reminders', 'sound', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
            </div>
          </div>

          {/* Privacy Settings */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <ShieldCheckIcon className="h-5 w-5 mr-2 text-primary-600" />
              Quyền riêng tư
            </h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-elderly-text">Chia sẻ dữ liệu</h3>
                  <p className="text-elderly-text-light text-sm">Cho phép chia sẻ dữ liệu để cải thiện dịch vụ</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.privacy.shareData}
                    onChange={(e) => handleSettingChange('privacy', 'shareData', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-elderly-text">Phân tích sử dụng</h3>
                  <p className="text-elderly-text-light text-sm">Cho phép thu thập dữ liệu phân tích</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.privacy.analytics}
                    onChange={(e) => handleSettingChange('privacy', 'analytics', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button className="btn btn-primary">
              Lưu cài đặt
            </button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default withPageAuthRequired(SettingsPage);
