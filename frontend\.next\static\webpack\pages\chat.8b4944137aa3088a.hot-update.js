"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./pages/chat/index.tsx":
/*!******************************!*\
  !*** ./pages/chat/index.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_PaperAirplaneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=PaperAirplaneIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=PaperAirplaneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst ChatPage = ()=>{\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            type: \"assistant\",\n            content: \"Xin ch\\xe0o! T\\xf4i l\\xe0 trợ l\\xfd AI sức khỏe. T\\xf4i c\\xf3 thể gi\\xfap bạn tư vấn về c\\xe1c vấn đề sức khỏe. Bạn cần hỗ trợ g\\xec h\\xf4m nay?\",\n            timestamp: new Date()\n        }\n    ]);\n    const handleSendMessage = ()=>{\n        if (!message.trim()) return;\n        // Add user message\n        const userMessage = {\n            id: messages.length + 1,\n            type: \"user\",\n            content: message,\n            timestamp: new Date()\n        };\n        // Simulate AI response\n        const aiResponse = {\n            id: messages.length + 2,\n            type: \"assistant\",\n            content: \"Cảm ơn bạn đ\\xe3 chia sẻ. Đ\\xe2y l\\xe0 phản hồi mẫu từ AI. Trong phi\\xean bản thực tế, t\\xf4i sẽ ph\\xe2n t\\xedch c\\xe2u hỏi của bạn v\\xe0 đưa ra lời tư vấn ph\\xf9 hợp.\",\n            timestamp: new Date()\n        };\n        setMessages([\n            ...messages,\n            userMessage,\n            aiResponse\n        ]);\n        setMessage(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Tư vấn AI\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-elderly-border\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-elderly-text\",\n                            children: \"Tư vấn AI sức khỏe\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-elderly-text-light mt-2\",\n                            children: \"Đặt c\\xe2u hỏi về sức khỏe v\\xe0 nhận tư vấn từ AI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n                    children: messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(msg.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg \".concat(msg.type === \"user\" ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-elderly-text\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: msg.content\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-1 \".concat(msg.type === \"user\" ? \"text-primary-100\" : \"text-elderly-text-light\"),\n                                        children: msg.timestamp.toLocaleTimeString(\"vi-VN\", {\n                                            hour: \"2-digit\",\n                                            minute: \"2-digit\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, undefined)\n                        }, msg.id, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-elderly-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: message,\n                                onChange: (e)=>setMessage(e.target.value),\n                                onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                placeholder: \"Nhập c\\xe2u hỏi của bạn...\",\n                                className: \"flex-1 form-input\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                className: \"btn btn-primary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PaperAirplaneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.PaperAirplaneIcon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatPage, \"3p/CIyESaL36YZ3/C5WroN4EZSA=\");\n_c = ChatPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(ChatPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/chat/index.tsx\n"));

/***/ })

});