"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./components/AIMessage.tsx":
/*!**********************************!*\
  !*** ./components/AIMessage.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst AIMessage = (param)=>{\n    let { content, timestamp } = param;\n    // Format AI response with proper line breaks and structure\n    const formatContent = (text)=>{\n        // Split by common patterns and format\n        const formatted = text// Replace ** with bold formatting\n        .replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\")// Replace * at start of line with bullet points\n        .replace(/^\\* /gm, \"• \")// Add line breaks for better readability\n        .replace(/\\. \\*/g, \".\\n\\n•\")// Format medical advice sections\n        .replace(/Chào bác\\/cô!/g, \"<strong>Ch\\xe0o b\\xe1c/c\\xf4!</strong>\").replace(/Nên tránh:/g, \"\\n<strong>\\uD83D\\uDEAB N\\xean tr\\xe1nh:</strong>\").replace(/Để giảm/g, \"\\n<strong>\\uD83D\\uDCA1 Để giảm\").replace(/Uống nhiều/g, \"\\n<strong>\\uD83D\\uDCA7 Uống nhiều\").replace(/Sức khỏe/g, \"\\n<strong>\\uD83C\\uDFE5 Sức khỏe\").replace(/Ngâm keo/g, \"\\n<strong>\\uD83C\\uDF3F Ng\\xe2m keo\").replace(/Thức ăn/g, \"\\n<strong>\\uD83C\\uDF4E Thức ăn\").replace(/Khói thuốc/g, \"\\n<strong>\\uD83D\\uDEAD Kh\\xf3i thuốc\").replace(/Trong trường hợp/g, \"\\n<strong>⚠️ Trong trường hợp\").replace(/Chúc bác\\/cô/g, \"\\n<strong>\\uD83C\\uDF1F Ch\\xfac b\\xe1c/c\\xf4\");\n        return formatted;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-4 shadow-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white text-sm font-bold\",\n                            children: \"AI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-800 leading-relaxed whitespace-pre-line\",\n                            dangerouslySetInnerHTML: {\n                                __html: formatContent(content)\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 pt-2 border-t border-blue-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-blue-600 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 mr-1\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    timestamp.toLocaleTimeString(\"vi-VN\", {\n                                        hour: \"2-digit\",\n                                        minute: \"2-digit\"\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-xs text-gray-500 bg-yellow-50 border border-yellow-200 rounded p-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"⚠️ Lưu \\xfd:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Đ\\xe2y chỉ l\\xe0 tư vấn sơ bộ. Vui l\\xf2ng tham khảo \\xfd kiến b\\xe1c sĩ chuy\\xean khoa để được chẩn đo\\xe1n v\\xe0 điều trị ch\\xednh x\\xe1c.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\AIMessage.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AIMessage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AIMessage);\nvar _c;\n$RefreshReg$(_c, \"AIMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AIMessage.tsx\n"));

/***/ }),

/***/ "./pages/chat/index.tsx":
/*!******************************!*\
  !*** ./pages/chat/index.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_PaperAirplaneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=PaperAirplaneIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=PaperAirplaneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _components_AIMessage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AIMessage */ \"./components/AIMessage.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ChatPage = ()=>{\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize chat session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeChat();\n    }, []);\n    const initializeChat = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Try to get active session first\n            let session = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatApi.getActiveSession();\n            // If no active session, create new one\n            if (!session) {\n                session = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatApi.createSession();\n            }\n            setCurrentSession(session);\n            // Load chat history if session has messages\n            if (session.messages && session.messages.length > 0) {\n                const formattedMessages = session.messages.map((msg)=>({\n                        id: msg.id,\n                        type: msg.message_type,\n                        content: msg.content,\n                        timestamp: new Date(msg.timestamp)\n                    }));\n                setMessages(formattedMessages);\n            } else {\n                // Add welcome message\n                setMessages([\n                    {\n                        id: 0,\n                        type: \"assistant\",\n                        content: \"Xin ch\\xe0o! T\\xf4i l\\xe0 trợ l\\xfd AI sức khỏe. T\\xf4i c\\xf3 thể gi\\xfap bạn tư vấn về c\\xe1c vấn đề sức khỏe. Bạn cần hỗ trợ g\\xec h\\xf4m nay?\",\n                        timestamp: new Date()\n                    }\n                ]);\n            }\n        } catch (err) {\n            console.error(\"Error initializing chat:\", err);\n            setError(\"Kh\\xf4ng thể khởi tạo phi\\xean chat. Vui l\\xf2ng thử lại.\");\n            // Fallback to offline mode\n            setMessages([\n                {\n                    id: 0,\n                    type: \"assistant\",\n                    content: \"Xin ch\\xe0o! T\\xf4i l\\xe0 trợ l\\xfd AI sức khỏe. Hiện tại đang c\\xf3 sự cố kết nối, nhưng t\\xf4i vẫn c\\xf3 thể hỗ trợ bạn với c\\xe1c c\\xe2u trả lời cơ bản.\",\n                    timestamp: new Date()\n                }\n            ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSendMessage = async ()=>{\n        if (!message.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now(),\n            type: \"user\",\n            content: message,\n            timestamp: new Date()\n        };\n        // Add user message immediately\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message;\n        setMessage(\"\");\n        setIsLoading(true);\n        setError(null);\n        try {\n            if (currentSession) {\n                // Send message to API\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatApi.sendMessage(currentSession.id, currentMessage);\n                // Add AI response\n                const aiMessage = {\n                    id: response.message.id,\n                    type: response.message.message_type,\n                    content: response.message.content,\n                    timestamp: new Date(response.message.timestamp)\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n            } else {\n                throw new Error(\"No active chat session\");\n            }\n        } catch (err) {\n            console.error(\"Error sending message:\", err);\n            setError(\"Kh\\xf4ng thể gửi tin nhắn. Vui l\\xf2ng thử lại.\");\n            // Add fallback response\n            const fallbackResponse = {\n                id: Date.now() + 1,\n                type: \"assistant\",\n                content: \"Xin lỗi, t\\xf4i đang gặp sự cố kỹ thuật. Vui l\\xf2ng thử lại sau hoặc li\\xean hệ b\\xe1c sĩ nếu cần hỗ trợ khẩn cấp.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    fallbackResponse\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Tư vấn AI\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-elderly-border\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-elderly-text\",\n                            children: \"Tư vấn AI sức khỏe\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-elderly-text-light mt-2\",\n                            children: \"Đặt c\\xe2u hỏi về sức khỏe v\\xe0 nhận tư vấn từ AI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, undefined),\n                        messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(msg.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                children: msg.type === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-primary-600 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: msg.content\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs mt-1 text-primary-100\",\n                                            children: msg.timestamp.toLocaleTimeString(\"vi-VN\", {\n                                                hour: \"2-digit\",\n                                                minute: \"2-digit\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-2xl w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIMessage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        content: msg.content,\n                                        timestamp: msg.timestamp\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, msg.id, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100 text-elderly-text\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Đang suy nghĩ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-elderly-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: message,\n                                onChange: (e)=>setMessage(e.target.value),\n                                onKeyDown: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                placeholder: \"Nhập c\\xe2u hỏi của bạn...\",\n                                className: \"flex-1 form-input\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: isLoading || !message.trim(),\n                                className: \"btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PaperAirplaneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PaperAirplaneIcon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\chat\\\\index.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatPage, \"Op21QLrHWbwLLRWqe42+dCfdxds=\");\n_c = ChatPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(ChatPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/chat/index.tsx\n"));

/***/ })

});