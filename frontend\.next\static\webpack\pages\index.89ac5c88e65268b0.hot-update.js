"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/Dashboard/Dashboard.tsx":
/*!********************************************!*\
  !*** ./components/Dashboard/Dashboard.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_CalendarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,CalendarIcon,HeartIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BeakerIcon,CalendarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst Dashboard = ()=>{\n    _s();\n    const { user } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        healthRecords: 0,\n        activeMedications: 0,\n        upcomingSchedules: 0,\n        weeklyReports: 0\n    });\n    const [todayReminders, setTodayReminders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Load dashboard stats in parallel\n            const [healthRecords, medications, schedules] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.healthApi.getRecords({\n                    limit: 100\n                }).catch(()=>[]),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.medicationsApi.getMedications(true).catch(()=>[]),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.schedulesApi.getSchedules({\n                    upcoming_only: true,\n                    limit: 10\n                }).catch(()=>[])\n            ]);\n            // Calculate stats\n            const now = new Date();\n            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n            const weeklyHealthRecords = healthRecords.filter((record)=>new Date(record.recorded_at) >= weekAgo);\n            setStats({\n                healthRecords: healthRecords.length,\n                activeMedications: medications.length,\n                upcomingSchedules: schedules.length,\n                weeklyReports: weeklyHealthRecords.length\n            });\n            // Load today's reminders\n            const today = new Date().toISOString().split(\"T\")[0];\n            const todaySchedules = schedules.filter((schedule)=>schedule.scheduled_at.startsWith(today));\n            const reminders = [\n                ...todaySchedules.map((schedule)=>({\n                        id: schedule.id,\n                        title: schedule.title,\n                        time: new Date(schedule.scheduled_at).toLocaleTimeString(\"vi-VN\", {\n                            hour: \"2-digit\",\n                            minute: \"2-digit\"\n                        }),\n                        type: \"appointment\",\n                        color: \"bg-blue-50 border-blue-200 text-blue-800\"\n                    })),\n                // Add medication reminders (simplified - could be enhanced)\n                ...medications.slice(0, 2).map((med, index)=>({\n                        id: \"med-\".concat(med.id),\n                        title: \"Uống \".concat(med.name),\n                        time: index === 0 ? \"8:00\" : \"20:00\",\n                        type: \"medication\",\n                        color: \"bg-yellow-50 border-yellow-200 text-yellow-800\"\n                    }))\n            ];\n            setTodayReminders(reminders.slice(0, 3)); // Limit to 3 reminders\n        } catch (err) {\n            console.error(\"Error loading dashboard data:\", err);\n            setError(\"Kh\\xf4ng thể tải dữ liệu dashboard\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const statsConfig = [\n        {\n            name: \"Chỉ số sức khỏe\",\n            value: stats.healthRecords.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HeartIcon,\n            color: \"text-red-600 bg-red-100\"\n        },\n        {\n            name: \"Thuốc đang d\\xf9ng\",\n            value: stats.activeMedications.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.BeakerIcon,\n            color: \"text-blue-600 bg-blue-100\"\n        },\n        {\n            name: \"Lịch hẹn sắp tới\",\n            value: stats.upcomingSchedules.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CalendarIcon,\n            color: \"text-green-600 bg-green-100\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-elderly-text\",\n                        children: [\n                            \"Xin ch\\xe0o, \",\n                            (user === null || user === void 0 ? void 0 : user.full_name) || \"Bạn\",\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-elderly-text-light mt-2\",\n                        children: \"Ch\\xe0o mừng bạn đến với hệ thống chăm s\\xf3c sức khỏe\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n                children: statsConfig.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg \".concat(stat.color),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-elderly-text\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elderly-text-light\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined)\n                    }, stat.name, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-elderly-text mb-4\",\n                            children: \"Nhắc nhở h\\xf4m nay\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: todayReminders.length > 0 ? todayReminders.map((reminder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-lg \".concat(reminder.color),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: reminder.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm opacity-75\",\n                                            children: reminder.time\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, reminder.id, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-gray-50 border border-gray-200 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-center\",\n                                    children: \"Kh\\xf4ng c\\xf3 nhắc nhở n\\xe0o h\\xf4m nay\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"v01TLY83yYEOsN2xuODkvGKFORY=\", false, function() {\n    return [\n        _lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Dashboard/Dashboard.tsx\n"));

/***/ })

});