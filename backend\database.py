"""
Database configuration and connection setup for Elderly Health Support System
"""

import os
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from decouple import config
import logging

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database URL from environment variables
DATABASE_URL = config(
    'DATABASE_URL',
    default='mysql://root:password@localhost/elderly_health_db'
)

# Create SQLAlchemy engine
try:
    engine = create_engine(
        DATABASE_URL,
        poolclass=StaticPool,
        pool_pre_ping=True,
        pool_recycle=300,
        echo=config('DEBUG', default=False, cast=bool)
    )
    logger.info("Database engine created successfully")
except Exception as e:
    logger.error(f"Failed to create database engine: {e}")
    raise

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class for models
Base = declarative_base()

# Metadata for database operations
metadata = MetaData()

def get_database():
    """
    Dependency function to get database session
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def init_database():
    """
    Initialize database tables
    """
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise

def check_database_connection():
    """
    Check if database connection is working
    """
    try:
        with engine.connect() as connection:
            result = connection.execute("SELECT 1")
            logger.info("Database connection successful")
            return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False

# Database utility functions
class DatabaseManager:
    """
    Database manager class for common operations
    """
    
    @staticmethod
    def get_session():
        """Get a new database session"""
        return SessionLocal()
    
    @staticmethod
    def execute_query(query: str, params: dict = None):
        """Execute a raw SQL query"""
        try:
            with engine.connect() as connection:
                if params:
                    result = connection.execute(query, params)
                else:
                    result = connection.execute(query)
                return result.fetchall()
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    @staticmethod
    def execute_procedure(procedure_name: str, params: list = None):
        """Execute a stored procedure"""
        try:
            with engine.connect() as connection:
                if params:
                    result = connection.execute(f"CALL {procedure_name}({','.join(['%s'] * len(params))})", params)
                else:
                    result = connection.execute(f"CALL {procedure_name}()")
                return result.fetchall()
        except Exception as e:
            logger.error(f"Procedure execution failed: {e}")
            raise

# Health check function
def health_check():
    """
    Perform database health check
    """
    try:
        db = SessionLocal()
        # Simple query to check connection
        db.execute("SELECT 1")
        db.close()
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"status": "unhealthy", "database": "disconnected", "error": str(e)}

# Connection pool monitoring
def get_pool_status():
    """
    Get connection pool status
    """
    try:
        pool = engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    except Exception as e:
        logger.error(f"Failed to get pool status: {e}")
        return {"error": str(e)}

# Database backup utilities
def backup_database(backup_path: str = None):
    """
    Create database backup (MySQL dump)
    """
    if not backup_path:
        backup_path = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
    
    try:
        import subprocess
        from datetime import datetime
        
        # Extract database info from URL
        db_info = DATABASE_URL.split('/')
        db_name = db_info[-1]
        host_info = db_info[2].split('@')[-1].split(':')
        host = host_info[0]
        port = host_info[1] if len(host_info) > 1 else '3306'
        
        user_info = DATABASE_URL.split('//')[1].split('@')[0].split(':')
        username = user_info[0]
        password = user_info[1]
        
        # Create mysqldump command
        cmd = [
            'mysqldump',
            f'--host={host}',
            f'--port={port}',
            f'--user={username}',
            f'--password={password}',
            '--single-transaction',
            '--routines',
            '--triggers',
            db_name
        ]
        
        with open(backup_path, 'w') as backup_file:
            subprocess.run(cmd, stdout=backup_file, check=True)
        
        logger.info(f"Database backup created: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Database backup failed: {e}")
        raise

# Initialize database on import
if __name__ == "__main__":
    # Test database connection
    if check_database_connection():
        print("✅ Database connection successful")
        init_database()
        print("✅ Database initialized")
    else:
        print("❌ Database connection failed")
