import React from "react";
import { withAuth } from "@/lib/auth";
import Layout from "@/components/Layout/Layout";

const HealthPage: React.FC = () => {
  return (
    <Layout title="<PERSON> dõi sức khỏe">
      <div className="p-6">
        <h1 className="text-3xl font-bold text-elderly-text mb-6">
          <PERSON> sức khỏe
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="card">
            <h2 className="text-xl font-semibold mb-4">Hu<PERSON><PERSON>t áp</h2>
            <p className="text-elderly-text-light">
              <PERSON><PERSON> nhận chỉ số huyết áp hàng ngày
            </p>
            <button className="btn btn-primary mt-4">Ghi nhận</button>
          </div>

          <div className="card">
            <h2 className="text-xl font-semibold mb-4"><PERSON><PERSON><PERSON><PERSON> huyết</h2>
            <p className="text-elderly-text-light"><PERSON> dõ<PERSON> mức đườ<PERSON> huyết</p>
            <button className="btn btn-primary mt-4">Ghi nhận</button>
          </div>

          <div className="card">
            <h2 className="text-xl font-semibold mb-4">Cân nặng</h2>
            <p className="text-elderly-text-light">Theo dõi cân nặng</p>
            <button className="btn btn-primary mt-4">Ghi nhận</button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default withAuth(HealthPage);
