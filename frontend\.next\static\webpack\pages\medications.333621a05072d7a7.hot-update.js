"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/medications",{

/***/ "./pages/medications/index.tsx":
/*!*************************************!*\
  !*** ./pages/medications/index.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst MedicationsPage = ()=>{\n    _s();\n    const [medications, setMedications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMed, setEditingMed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInactive, setShowInactive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadMedications();\n    }, [\n        showInactive\n    ]);\n    const loadMedications = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.medicationsApi.getMedications(!showInactive);\n            setMedications(data);\n        } catch (err) {\n            console.error(\"Error loading medications:\", err);\n            setError(\"Kh\\xf4ng thể tải danh s\\xe1ch thuốc\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Bạn c\\xf3 chắc chắn muốn x\\xf3a thuốc n\\xe0y?\")) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_4__.medicationsApi.deleteMedication(id);\n            await loadMedications();\n        } catch (err) {\n            console.error(\"Error deleting medication:\", err);\n            setError(\"Kh\\xf4ng thể x\\xf3a thuốc\");\n        }\n    };\n    const handleAdd = ()=>{\n        setEditingMed(null);\n        setShowAddForm(true);\n    };\n    const handleEdit = (medication)=>{\n        setEditingMed(medication);\n        setShowAddForm(true);\n    };\n    const getStatusColor = (medication)=>{\n        if (!medication.is_active) {\n            return \"bg-gray-100 text-gray-800\";\n        }\n        if (medication.end_date) {\n            const endDate = new Date(medication.end_date);\n            const today = new Date();\n            const daysLeft = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            if (daysLeft <= 0) {\n                return \"bg-red-100 text-red-800\";\n            } else if (daysLeft <= 7) {\n                return \"bg-yellow-100 text-yellow-800\";\n            }\n        }\n        return \"bg-green-100 text-green-800\";\n    };\n    const getStatusText = (medication)=>{\n        if (!medication.is_active) {\n            return \"Đ\\xe3 ngừng\";\n        }\n        if (medication.end_date) {\n            const endDate = new Date(medication.end_date);\n            const today = new Date();\n            const daysLeft = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            if (daysLeft <= 0) {\n                return \"Hết hạn\";\n            } else if (daysLeft <= 7) {\n                return \"C\\xf2n \".concat(daysLeft, \" ng\\xe0y\");\n            }\n        }\n        return \"Đang d\\xf9ng\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"Quản l\\xfd thuốc\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Quản l\\xfd thuốc\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-elderly-text\",\n                            children: \"Quản l\\xfd thuốc\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowInactive(!showInactive),\n                                    className: \"btn \".concat(showInactive ? \"btn-secondary\" : \"btn-outline\"),\n                                    children: showInactive ? \"Ẩn thuốc đ\\xe3 ngừng\" : \"Hiện thuốc đ\\xe3 ngừng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAdd,\n                                    className: \"btn btn-primary flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PlusIcon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Th\\xeam thuốc mới\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadMedications,\n                            className: \"mt-2 text-red-600 hover:text-red-800 text-sm underline\",\n                            children: \"Thử lại\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: medications.length > 0 ? medications.map((medication)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-elderly-text\",\n                                                        children: medication.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 rounded-full text-sm \".concat(getStatusColor(medication)),\n                                                        children: getStatusText(medication)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-elderly-text-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Liều d\\xf9ng:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                        lineNumber: 190,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    medication.dosage\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-elderly-text-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Tần suất:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    medication.frequency\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-elderly-text-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Bắt đầu:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                        lineNumber: 198,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    new Date(medication.start_date).toLocaleDateString(\"vi-VN\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            medication.end_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-elderly-text-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Kết th\\xfac:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    new Date(medication.end_date).toLocaleDateString(\"vi-VN\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            medication.instructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-800 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ClockIcon, {\n                                                            className: \"h-4 w-4 inline mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Hướng dẫn:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        \" \",\n                                                        medication.instructions\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEdit(medication),\n                                                className: \"p-2 text-blue-600 hover:bg-blue-50 rounded-lg\",\n                                                title: \"Chỉnh sửa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PencilIcon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDelete(medication.id),\n                                                className: \"p-2 text-red-600 hover:bg-red-50 rounded-lg\",\n                                                title: \"X\\xf3a\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.TrashIcon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 17\n                            }, undefined)\n                        }, medication.id, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 15\n                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83D\\uDC8A\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-elderly-text mb-2\",\n                                children: showInactive ? \"Kh\\xf4ng c\\xf3 thuốc đ\\xe3 ngừng\" : \"Chưa c\\xf3 thuốc n\\xe0o\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-elderly-text-light mb-6\",\n                                children: showInactive ? \"Bạn chưa c\\xf3 thuốc n\\xe0o đ\\xe3 ngừng sử dụng\" : \"H\\xe3y th\\xeam thuốc đầu ti\\xean để bắt đầu theo d\\xf5i\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, undefined),\n                            !showInactive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAdd,\n                                className: \"btn btn-primary\",\n                                children: \"Th\\xeam thuốc đầu ti\\xean\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined),\n                medications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-green-600\",\n                                    children: medications.filter((m)=>m.is_active).length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light\",\n                                    children: \"Thuốc đang d\\xf9ng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-yellow-600\",\n                                    children: medications.filter((m)=>{\n                                        if (!m.end_date || !m.is_active) return false;\n                                        const daysLeft = Math.ceil((new Date(m.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));\n                                        return daysLeft <= 7 && daysLeft > 0;\n                                    }).length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light\",\n                                    children: \"Sắp hết hạn\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-gray-600\",\n                                    children: medications.filter((m)=>!m.is_active).length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light\",\n                                    children: \"Đ\\xe3 ngừng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MedicationsPage, \"B5C7c7rwEA2s2ja34ie9P6pPkH4=\");\n_c = MedicationsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(MedicationsPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"MedicationsPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/medications/index.tsx\n"));

/***/ })

});