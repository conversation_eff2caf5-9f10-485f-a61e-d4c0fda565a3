# Hệ thống hỗ trợ theo dõi & chă<PERSON> sóc sức khỏe người cao tuổi
## Elderly Health Support System

Hệ thống web hỗ trợ người cao tuổi và người thân trong việc theo dõi sức khỏe hàng ngày.

## 🚀 Công nghệ sử dụng

- **Frontend**: Next.js + Tailwind CSS
- **Backend**: FastAPI (Python)
- **Database**: MySQL
- **Authentication**: Auth0 với OTP
- **Charts**: Chart.js/Recharts
- **AI Chatbot**: OpenAI API (hoặc responses mẫu)

## 📋 Chức năng chính

### 1. Qu<PERSON>n lý hồ sơ sức khỏe
- Thông tin cá nhân (họ tên, tuổi, địa chỉ, li<PERSON>n hệ)
- Thông tin sức khỏe tổng quát (bệ<PERSON> nền, d<PERSON>, ghi chú)

### 2. <PERSON> dõ<PERSON> chỉ số sức khỏe
- <PERSON><PERSON> <PERSON>hận các chỉ số: h<PERSON><PERSON><PERSON> <PERSON><PERSON>, nh<PERSON><PERSON> tim, đ<PERSON><PERSON><PERSON>, cân nặng
- <PERSON><PERSON> lịch sử và biểu đồ theo ng<PERSON>/tháng

### 3. Nhắc nhở và lịch hẹn
- Đặt lịch uống thuốc
- Đặt lịch khám bệnh
- Hiển thị thông báo nhắc nhở

### 4. Chatbot AI tư vấn sức khỏe
- Giao diện chat thân thiện
- Tư vấn sức khỏe cơ bản

### 5. Xác thực người dùng
- Đăng ký/Đăng nhập với Auth0
- Xác thực OTP

## 🛠️ Cài đặt và chạy

### Yêu cầu hệ thống
- Node.js 18+
- Python 3.8+
- MySQL 8.0+
- Git

### 1. Clone repository
```bash
git clone <repository-url>
cd SucKhoe
```

### 2. Cài đặt Backend (FastAPI)
```bash
cd backend
pip install -r requirements.txt

# Cấu hình database
cp .env.example .env
# Chỉnh sửa file .env với thông tin database và Auth0

# Chạy migrations
python -m alembic upgrade head

# Khởi chạy server
uvicorn main:app --reload --port 8000
```

### 3. Cài đặt Frontend (Next.js)
```bash
cd frontend
npm install

# Cấu hình environment
cp .env.local.example .env.local
# Chỉnh sửa file .env.local với API URLs và Auth0 config

# Khởi chạy development server
npm run dev
```

### 4. Cài đặt Database (MySQL)
```bash
# Tạo database
mysql -u root -p
CREATE DATABASE elderly_health_db;

# Import schema
mysql -u root -p elderly_health_db < database/schema.sql

# Import sample data (optional)
mysql -u root -p elderly_health_db < database/sample_data.sql
```

## 🔧 Cấu hình Auth0

### 1. Tạo Auth0 Application
1. Đăng ký tài khoản tại [Auth0](https://auth0.com)
2. Tạo new Application (Single Page Application)
3. Cấu hình Allowed Callback URLs: `http://localhost:3000/api/auth/callback`
4. Cấu hình Allowed Logout URLs: `http://localhost:3000`

### 2. Cấu hình OTP
1. Trong Auth0 Dashboard, vào Authentication > Multifactor Auth
2. Enable SMS factor
3. Cấu hình SMS provider (Twilio recommended)

### 3. Environment Variables
```env
# Frontend (.env.local)
AUTH0_SECRET=your-auth0-secret
AUTH0_BASE_URL=http://localhost:3000
AUTH0_ISSUER_BASE_URL=https://your-domain.auth0.com
AUTH0_CLIENT_ID=your-client-id
AUTH0_CLIENT_SECRET=your-client-secret

# Backend (.env)
AUTH0_DOMAIN=your-domain.auth0.com
AUTH0_API_AUDIENCE=your-api-audience
DATABASE_URL=mysql://user:password@localhost/elderly_health_db
OPENAI_API_KEY=your-openai-api-key
```

## 📱 Sử dụng

1. Truy cập `http://localhost:3000`
2. Đăng ký tài khoản mới hoặc đăng nhập
3. Hoàn thành hồ sơ sức khỏe
4. Bắt đầu ghi nhận các chỉ số sức khỏe
5. Đặt lịch nhắc nhở uống thuốc/khám bệnh
6. Sử dụng chatbot để tư vấn sức khỏe

## 📊 Cấu trúc Database

Xem chi tiết trong `database/schema.sql`:
- `users` - Thông tin người dùng
- `health_profiles` - Hồ sơ sức khỏe
- `health_records` - Chỉ số sức khỏe
- `medications` - Thông tin thuốc
- `schedules` - Lịch hẹn
- `reminders` - Nhắc nhở
- `chat_sessions` - Lịch sử chat

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Liên hệ

Project Link: [https://github.com/your-username/SucKhoe](https://github.com/your-username/SucKhoe)
