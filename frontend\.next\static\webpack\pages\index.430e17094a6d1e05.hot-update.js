"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/Dashboard/Dashboard.tsx":
/*!********************************************!*\
  !*** ./components/Dashboard/Dashboard.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,CalendarIcon,ChartBarIcon,HeartIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChartBarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst Dashboard = ()=>{\n    _s();\n    const { user } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        healthRecords: 0,\n        activeMedications: 0,\n        upcomingSchedules: 0,\n        weeklyReports: 0\n    });\n    const [todayReminders, setTodayReminders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Load dashboard stats in parallel\n            const [healthRecords, medications, schedules] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.healthApi.getRecords({\n                    limit: 100\n                }).catch(()=>[]),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.medicationApi.getMedications(true).catch(()=>[]),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.scheduleApi.getSchedules({\n                    upcoming_only: true,\n                    limit: 10\n                }).catch(()=>[])\n            ]);\n            // Calculate stats\n            const now = new Date();\n            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n            const weeklyHealthRecords = healthRecords.filter((record)=>new Date(record.recorded_at) >= weekAgo);\n            setStats({\n                healthRecords: healthRecords.length,\n                activeMedications: medications.length,\n                upcomingSchedules: schedules.length,\n                weeklyReports: weeklyHealthRecords.length\n            });\n            // Load today's reminders\n            const today = new Date().toISOString().split(\"T\")[0];\n            const todaySchedules = schedules.filter((schedule)=>schedule.scheduled_at.startsWith(today));\n            const reminders = [\n                ...todaySchedules.map((schedule)=>({\n                        id: schedule.id,\n                        title: schedule.title,\n                        time: new Date(schedule.scheduled_at).toLocaleTimeString(\"vi-VN\", {\n                            hour: \"2-digit\",\n                            minute: \"2-digit\"\n                        }),\n                        type: \"appointment\",\n                        color: \"bg-blue-50 border-blue-200 text-blue-800\"\n                    })),\n                // Add medication reminders (simplified - could be enhanced)\n                ...medications.slice(0, 2).map((med, index)=>({\n                        id: \"med-\".concat(med.id),\n                        title: \"Uống \".concat(med.name),\n                        time: index === 0 ? \"8:00\" : \"20:00\",\n                        type: \"medication\",\n                        color: \"bg-yellow-50 border-yellow-200 text-yellow-800\"\n                    }))\n            ];\n            setTodayReminders(reminders.slice(0, 3)); // Limit to 3 reminders\n        } catch (err) {\n            console.error(\"Error loading dashboard data:\", err);\n            setError(\"Kh\\xf4ng thể tải dữ liệu dashboard\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const statsConfig = [\n        {\n            name: \"Chỉ số sức khỏe\",\n            value: stats.healthRecords.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HeartIcon,\n            color: \"text-red-600 bg-red-100\"\n        },\n        {\n            name: \"Thuốc đang d\\xf9ng\",\n            value: stats.activeMedications.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.BeakerIcon,\n            color: \"text-blue-600 bg-blue-100\"\n        },\n        {\n            name: \"Lịch hẹn sắp tới\",\n            value: stats.upcomingSchedules.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CalendarIcon,\n            color: \"text-green-600 bg-green-100\"\n        },\n        {\n            name: \"B\\xe1o c\\xe1o tuần n\\xe0y\",\n            value: stats.weeklyReports.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChartBarIcon,\n            color: \"text-purple-600 bg-purple-100\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-elderly-text\",\n                        children: [\n                            \"Xin ch\\xe0o, \",\n                            (user === null || user === void 0 ? void 0 : user.full_name) || \"Bạn\",\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-elderly-text-light mt-2\",\n                        children: \"Ch\\xe0o mừng bạn đến với hệ thống chăm s\\xf3c sức khỏe\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadDashboardData,\n                                className: \"mt-2 text-red-600 hover:text-red-800 text-sm underline\",\n                                children: \"Thử lại\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: statsConfig.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg \".concat(stat.color),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-elderly-text\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elderly-text-light\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined)\n                    }, stat.name, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-elderly-text mb-4\",\n                                children: \"H\\xe0nh động nhanh\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/health\",\n                                        className: \"w-full btn btn-primary text-left\",\n                                        children: \"Ghi nhận chỉ số sức khỏe\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/schedules\",\n                                        className: \"w-full btn btn-secondary text-left\",\n                                        children: \"Đặt lịch nhắc nhở\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/chat\",\n                                        className: \"w-full btn btn-secondary text-left\",\n                                        children: \"Tư vấn với AI\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-elderly-text mb-4\",\n                                children: \"Nhắc nhở h\\xf4m nay\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: todayReminders.length > 0 ? todayReminders.map((reminder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 border rounded-lg \".concat(reminder.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: reminder.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm opacity-75\",\n                                                children: reminder.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, reminder.id, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gray-50 border border-gray-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-center\",\n                                        children: \"Kh\\xf4ng c\\xf3 nhắc nhở n\\xe0o h\\xf4m nay\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"v01TLY83yYEOsN2xuODkvGKFORY=\", false, function() {\n    return [\n        _lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Dashboard/Dashboard.tsx\n"));

/***/ })

});