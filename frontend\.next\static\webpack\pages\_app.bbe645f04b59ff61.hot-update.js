"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./lib/auth.tsx":
/*!**********************!*\
  !*** ./lib/auth.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useUser: function() { return /* binding */ useUser; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/**\n * Simple authentication context and hooks\n */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            // Try to get token from cookie first, then localStorage\n            let savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"auth_token\");\n            if (!savedToken && \"object\" !== \"undefined\") {\n                savedToken = localStorage.getItem(\"auth_token\");\n            }\n            if (savedToken) {\n                setToken(savedToken);\n                try {\n                    // Verify token and get user info\n                    const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(API_BASE_URL, \"/auth/me\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(savedToken)\n                        }\n                    });\n                    setUser(response.data);\n                    // Ensure token is saved in both places\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", savedToken, {\n                        expires: 7\n                    }); // 7 days\n                    if (true) {\n                        localStorage.setItem(\"auth_token\", savedToken);\n                    }\n                } catch (error) {\n                    console.error(\"Token verification failed:\", error);\n                    // Remove invalid token from both places\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n                    if (true) {\n                        localStorage.removeItem(\"auth_token\");\n                    }\n                    setToken(null);\n                }\n            }\n            setIsLoading(false);\n        };\n        initAuth();\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"\".concat(API_BASE_URL, \"/auth/login\"), {\n                email,\n                password\n            });\n            const { access_token, user: userData } = response.data;\n            // Save token to cookie\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", access_token, {\n                expires: 1\n            }); // 1 day\n            setToken(access_token);\n            setUser(userData);\n            // Redirect to dashboard\n            router.push(\"/\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Login failed:\", error);\n            throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Login failed. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (email, password, full_name, phone)=>{\n        try {\n            setIsLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"\".concat(API_BASE_URL, \"/auth/register\"), {\n                email,\n                password,\n                full_name,\n                phone\n            });\n            const { access_token, user: userData } = response.data;\n            // Save token to cookie\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", access_token, {\n                expires: 1\n            }); // 1 day\n            setToken(access_token);\n            setUser(userData);\n            // Redirect to dashboard\n            router.push(\"/\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Registration failed:\", error);\n            throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Registration failed. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        // Remove token from cookie\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n        setToken(null);\n        setUser(null);\n        // Redirect to home\n        router.push(\"/\");\n    };\n    const value = {\n        user,\n        isLoading,\n        login,\n        register,\n        logout,\n        token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n        lineNumber: 173,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AuthProvider, \"D92vKOVhjWcaChOYbuVhF88zu6M=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// HOC for protected pages\nconst withAuth = (Component)=>{\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { user, isLoading } = useAuth();\n        const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!isLoading && !user) {\n                router.push(\"/auth/login\");\n            }\n        }, [\n            user,\n            isLoading,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            return null; // Will redirect\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n            lineNumber: 210,\n            columnNumber: 12\n        }, this);\n    }, \"FG2DkPvrCUydgYqHSyFl4Q88edA=\", false, function() {\n        return [\n            useAuth,\n            next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n        ];\n    });\n};\n// Hook for checking if user is authenticated\nconst useUser = ()=>{\n    _s2();\n    const { user, isLoading } = useAuth();\n    return {\n        user,\n        isLoading,\n        error: null\n    };\n};\n_s2(useUser, \"6lKHjqCqGIRsHh92bje8H78laow=\", false, function() {\n    return [\n        useAuth\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/auth.tsx\n"));

/***/ })

});