# Database Configuration
DATABASE_URL=mysql://username:password@localhost/elderly_health_db
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=elderly_health_db

# Auth0 Configuration
AUTH0_DOMAIN=your-domain.auth0.com
AUTH0_API_AUDIENCE=your-api-audience
AUTH0_ISSUER=https://your-domain.auth0.com/
AUTH0_ALGORITHMS=RS256

# OpenAI Configuration (for AI Chatbot)
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo

# Application Settings
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Environment
ENVIRONMENT=development
DEBUG=True

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# File Upload Settings
MAX_FILE_SIZE=5242880  # 5MB
UPLOAD_DIR=uploads/

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
