"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "__barrel_optimize__?names=BeakerIcon,CalendarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*****************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BeakerIcon,CalendarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*****************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BeakerIcon: function() { return /* reexport safe */ _BeakerIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   CalendarIcon: function() { return /* reexport safe */ _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   HeartIcon: function() { return /* reexport safe */ _HeartIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _BeakerIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BeakerIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalendarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _HeartIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HeartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CZWFrZXJJY29uLENhbGVuZGFySWNvbixIZWFydEljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDdUQ7QUFDSSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz83YTIyIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWFrZXJJY29uIH0gZnJvbSBcIi4vQmVha2VySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGVuZGFySWNvbiB9IGZyb20gXCIuL0NhbGVuZGFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhlYXJ0SWNvbiB9IGZyb20gXCIuL0hlYXJ0SWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BeakerIcon,CalendarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./components/Dashboard/Dashboard.tsx":
/*!********************************************!*\
  !*** ./components/Dashboard/Dashboard.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_CalendarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,CalendarIcon,HeartIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BeakerIcon,CalendarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst Dashboard = ()=>{\n    _s();\n    const { user } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        healthRecords: 0,\n        activeMedications: 0,\n        upcomingSchedules: 0,\n        weeklyReports: 0\n    });\n    const [todayReminders, setTodayReminders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Load dashboard stats in parallel\n            const [healthRecords, medications, schedules] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.healthApi.getRecords({\n                    limit: 100\n                }).catch(()=>[]),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.medicationsApi.getMedications(true).catch(()=>[]),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.schedulesApi.getSchedules({\n                    upcoming_only: true,\n                    limit: 10\n                }).catch(()=>[])\n            ]);\n            // Calculate stats\n            const now = new Date();\n            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n            const weeklyHealthRecords = healthRecords.filter((record)=>new Date(record.recorded_at) >= weekAgo);\n            setStats({\n                healthRecords: healthRecords.length,\n                activeMedications: medications.length,\n                upcomingSchedules: schedules.length,\n                weeklyReports: weeklyHealthRecords.length\n            });\n            // Load today's reminders\n            const today = new Date().toISOString().split(\"T\")[0];\n            const todaySchedules = schedules.filter((schedule)=>schedule.scheduled_at.startsWith(today));\n            const reminders = [\n                ...todaySchedules.map((schedule)=>({\n                        id: schedule.id,\n                        title: schedule.title,\n                        time: new Date(schedule.scheduled_at).toLocaleTimeString(\"vi-VN\", {\n                            hour: \"2-digit\",\n                            minute: \"2-digit\"\n                        }),\n                        type: \"appointment\",\n                        color: \"bg-blue-50 border-blue-200 text-blue-800\"\n                    })),\n                // Add medication reminders (simplified - could be enhanced)\n                ...medications.slice(0, 2).map((med, index)=>({\n                        id: \"med-\".concat(med.id),\n                        title: \"Uống \".concat(med.name),\n                        time: index === 0 ? \"8:00\" : \"20:00\",\n                        type: \"medication\",\n                        color: \"bg-yellow-50 border-yellow-200 text-yellow-800\"\n                    }))\n            ];\n            setTodayReminders(reminders.slice(0, 3)); // Limit to 3 reminders\n        } catch (err) {\n            console.error(\"Error loading dashboard data:\", err);\n            setError(\"Kh\\xf4ng thể tải dữ liệu dashboard\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const statsConfig = [\n        {\n            name: \"Chỉ số sức khỏe\",\n            value: stats.healthRecords.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HeartIcon,\n            color: \"text-red-600 bg-red-100\"\n        },\n        {\n            name: \"Thuốc đang d\\xf9ng\",\n            value: stats.activeMedications.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.BeakerIcon,\n            color: \"text-blue-600 bg-blue-100\"\n        },\n        {\n            name: \"Lịch hẹn sắp tới\",\n            value: stats.upcomingSchedules.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CalendarIcon,\n            color: \"text-green-600 bg-green-100\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-elderly-text\",\n                        children: [\n                            \"Xin ch\\xe0o, \",\n                            (user === null || user === void 0 ? void 0 : user.full_name) || \"Bạn\",\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-elderly-text-light mt-2\",\n                        children: \"Ch\\xe0o mừng bạn đến với hệ thống chăm s\\xf3c sức khỏe\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: statsConfig.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg \".concat(stat.color),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-elderly-text\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elderly-text-light\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined)\n                    }, stat.name, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-elderly-text mb-4\",\n                                children: \"H\\xe0nh động nhanh\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/health\",\n                                        className: \"w-full btn btn-primary text-left\",\n                                        children: \"Ghi nhận chỉ số sức khỏe\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/schedules\",\n                                        className: \"w-full btn btn-secondary text-left\",\n                                        children: \"Đặt lịch nhắc nhở\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/chat\",\n                                        className: \"w-full btn btn-secondary text-left\",\n                                        children: \"Tư vấn với AI\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-elderly-text mb-4\",\n                                children: \"Nhắc nhở h\\xf4m nay\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: todayReminders.length > 0 ? todayReminders.map((reminder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 border rounded-lg \".concat(reminder.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: reminder.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm opacity-75\",\n                                                children: reminder.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, reminder.id, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gray-50 border border-gray-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-center\",\n                                        children: \"Kh\\xf4ng c\\xf3 nhắc nhở n\\xe0o h\\xf4m nay\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"v01TLY83yYEOsN2xuODkvGKFORY=\", false, function() {\n    return [\n        _lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Dashboard/Dashboard.tsx\n"));

/***/ })

});