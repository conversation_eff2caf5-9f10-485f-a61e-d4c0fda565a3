"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/health",{

/***/ "./pages/health/index.tsx":
/*!********************************!*\
  !*** ./pages/health/index.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst HealthPage = ()=>{\n    _s();\n    const [records, setRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const healthTypes = [\n        {\n            type: \"blood_pressure\",\n            name: \"Huyết \\xe1p\",\n            unit: \"mmHg\",\n            description: \"Ghi nhận chỉ số huyết \\xe1p h\\xe0ng ng\\xe0y\",\n            color: \"bg-red-50 border-red-200 text-red-800\",\n            icon: \"❤️\"\n        },\n        {\n            type: \"blood_sugar\",\n            name: \"Đường huyết\",\n            unit: \"mg/dL\",\n            description: \"Theo d\\xf5i mức đường huyết\",\n            color: \"bg-blue-50 border-blue-200 text-blue-800\",\n            icon: \"\\uD83E\\uDE78\"\n        },\n        {\n            type: \"weight\",\n            name: \"C\\xe2n nặng\",\n            unit: \"kg\",\n            description: \"Theo d\\xf5i c\\xe2n nặng\",\n            color: \"bg-green-50 border-green-200 text-green-800\",\n            icon: \"⚖️\"\n        },\n        {\n            type: \"heart_rate\",\n            name: \"Nhịp tim\",\n            unit: \"bpm\",\n            description: \"Theo d\\xf5i nhịp tim\",\n            color: \"bg-purple-50 border-purple-200 text-purple-800\",\n            icon: \"\\uD83D\\uDC93\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadHealthData();\n    }, []);\n    const loadHealthData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Load recent records\n            const recentRecords = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.healthApi.getRecords({\n                limit: 20\n            });\n            setRecords(recentRecords);\n            // Load stats for each health type\n            const statsPromises = healthTypes.map(async (type)=>{\n                try {\n                    return await _lib_api__WEBPACK_IMPORTED_MODULE_4__.healthApi.getStats(type.type);\n                } catch (e) {\n                    return {\n                        record_type: type.type,\n                        total_records: 0,\n                        latest_value: null,\n                        latest_date: null,\n                        average_last_7_days: null,\n                        trend: \"stable\"\n                    };\n                }\n            });\n            const statsResults = await Promise.all(statsPromises);\n            setStats(statsResults);\n        } catch (err) {\n            console.error(\"Error loading health data:\", err);\n            setError(\"Kh\\xf4ng thể tải dữ liệu sức khỏe\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddRecord = (type)=>{\n        setSelectedType(type);\n        setShowAddForm(true);\n    };\n    const handleDeleteRecord = async (recordId)=>{\n        if (!confirm(\"Bạn c\\xf3 chắc chắn muốn x\\xf3a bản ghi n\\xe0y?\")) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_4__.healthApi.deleteRecord(recordId);\n            await loadHealthData();\n        } catch (err) {\n            console.error(\"Error deleting health record:\", err);\n            setError(\"Kh\\xf4ng thể x\\xf3a bản ghi\");\n        }\n    };\n    const getTypeConfig = (type)=>{\n        return healthTypes.find((t)=>t.type === type) || healthTypes[0];\n    };\n    const getTypeStats = (type)=>{\n        return stats.find((s)=>s.record_type === type);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"Theo d\\xf5i sức khỏe\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Theo d\\xf5i sức khỏe\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-elderly-text\",\n                            children: \"Theo d\\xf5i sức khỏe\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"btn btn-primary flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PlusIcon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ghi nhận mới\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadHealthData,\n                            className: \"mt-2 text-red-600 hover:text-red-800 text-sm underline\",\n                            children: \"Thử lại\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: healthTypes.map((type)=>{\n                        const typeStats = getTypeStats(type.type);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: type.icon\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: type.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon, {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light text-sm mb-4\",\n                                    children: type.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, undefined),\n                                typeStats && typeStats.latest_value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-elderly-text\",\n                                            children: typeStats.latest_value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-elderly-text-light\",\n                                            children: typeStats.latest_date ? new Date(typeStats.latest_date).toLocaleDateString(\"vi-VN\") : \"Chưa c\\xf3 dữ liệu\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-elderly-text-light\",\n                                            children: [\n                                                \"Tổng: \",\n                                                typeStats.total_records,\n                                                \" lần ghi nhận\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm\",\n                                        children: \"Chưa c\\xf3 dữ liệu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleAddRecord(type.type),\n                                    className: \"btn btn-primary w-full\",\n                                    children: \"Ghi nhận\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, type.type, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Ghi nhận gần đ\\xe2y\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined),\n                        records.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: records.slice(0, 10).map((record)=>{\n                                const typeConfig = getTypeConfig(record.record_type);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-lg \".concat(typeConfig.color),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            typeConfig.icon,\n                                                            \" \",\n                                                            typeConfig.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-bold\",\n                                                        children: record.display_value\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleDeleteRecord(record.id),\n                                                            className: \"p-1 text-red-600 hover:bg-red-50 rounded\",\n                                                            title: \"X\\xf3a\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.TrashIcon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: new Date(record.recorded_at).toLocaleDateString(\"vi-VN\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs\",\n                                                        children: new Date(record.recorded_at).toLocaleTimeString(\"vi-VN\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block px-2 py-1 rounded-full text-xs \".concat(record.is_normal ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                        children: record.is_normal ? \"B\\xecnh thường\" : \"Cần ch\\xfa \\xfd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, record.id, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Chưa c\\xf3 ghi nhận n\\xe0o\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddForm(true),\n                                    className: \"mt-4 btn btn-primary\",\n                                    children: \"Th\\xeam ghi nhận đầu ti\\xean\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\health\\\\index.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HealthPage, \"zolw/uRPK8REVhDJw+vLVnCg1Ok=\");\n_c = HealthPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(HealthPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"HealthPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/health/index.tsx\n"));

/***/ })

});