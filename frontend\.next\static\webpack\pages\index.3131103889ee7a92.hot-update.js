"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/Landing/LandingPage.tsx":
/*!********************************************!*\
  !*** ./components/Landing/LandingPage.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,HeartIcon,ShieldCheckIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,HeartIcon,ShieldCheckIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/**\n * Landing page component for non-authenticated users\n */ \n\n\n\nconst features = [\n    {\n        name: \"Theo d\\xf5i sức khỏe\",\n        description: \"Ghi nhận v\\xe0 theo d\\xf5i c\\xe1c chỉ số sức khỏe quan trọng như huyết \\xe1p, đường huyết, nhịp tim.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.HeartIcon\n    },\n    {\n        name: \"Biểu đồ trực quan\",\n        description: \"Xem biểu đồ thay đổi sức khỏe theo thời gian để hiểu r\\xf5 t\\xecnh trạng của bạn.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon\n    },\n    {\n        name: \"Nhắc nhở th\\xf4ng minh\",\n        description: \"Nhận nhắc nhở uống thuốc v\\xe0 lịch kh\\xe1m bệnh đ\\xfang giờ, kh\\xf4ng bao giờ qu\\xean.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ClockIcon\n    },\n    {\n        name: \"Tư vấn AI\",\n        description: \"Chatbot AI th\\xf4ng minh tư vấn sức khỏe 24/7, lu\\xf4n sẵn s\\xe0ng hỗ trợ bạn.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChatBubbleLeftRightIcon\n    },\n    {\n        name: \"Bảo mật cao\",\n        description: \"Th\\xf4ng tin sức khỏe được bảo vệ an to\\xe0n với c\\xf4ng nghệ m\\xe3 h\\xf3a ti\\xean tiến.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ShieldCheckIcon\n    },\n    {\n        name: \"Dễ sử dụng\",\n        description: \"Giao diện th\\xe2n thiện, ph\\xf9 hợp với người cao tuổi, dễ d\\xe0ng sử dụng.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.UserGroupIcon\n    }\n];\nconst stats = [\n    {\n        name: \"Người d\\xf9ng tin tưởng\",\n        value: \"1,000+\"\n    },\n    {\n        name: \"Chỉ số sức khỏe được theo d\\xf5i\",\n        value: \"50,000+\"\n    },\n    {\n        name: \"Lời nhắc đ\\xe3 gửi\",\n        value: \"100,000+\"\n    },\n    {\n        name: \"Tư vấn AI\",\n        value: \"24/7\"\n    }\n];\nconst LandingPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative isolate px-6 pt-14 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary-400 to-primary-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-4xl py-32 sm:py-48 lg:py-56\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold tracking-tight text-elderly-text sm:text-6xl\",\n                                    children: [\n                                        \"Chăm s\\xf3c sức khỏe\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-600\",\n                                            children: \"người cao tuổi\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" th\\xf4ng minh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-lg leading-8 text-elderly-text-light max-w-2xl mx-auto\",\n                                    children: \"Hệ thống hỗ trợ theo d\\xf5i v\\xe0 chăm s\\xf3c sức khỏe to\\xe0n diện, gi\\xfap người cao tuổi v\\xe0 gia đ\\xecnh quản l\\xfd sức khỏe một c\\xe1ch dễ d\\xe0ng v\\xe0 hiệu quả.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/register\",\n                                            className: \"btn btn-primary btn-lg\",\n                                            children: \"Đăng k\\xfd miễn ph\\xed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/login\",\n                                            className: \"btn btn-outline btn-lg\",\n                                            children: \"Đăng nhập\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary-400 to-primary-600 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"features\",\n                className: \"py-24 sm:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl lg:text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-base font-semibold leading-7 text-primary-600\",\n                                    children: \"T\\xednh năng nổi bật\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-3xl font-bold tracking-tight text-elderly-text sm:text-4xl\",\n                                    children: \"Mọi thứ bạn cần để chăm s\\xf3c sức khỏe\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-lg leading-8 text-elderly-text-light\",\n                                    children: \"Hệ thống được thiết kế đặc biệt cho người cao tuổi với giao diện đơn giản, t\\xednh năng to\\xe0n diện v\\xe0 hỗ trợ 24/7.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                className: \"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\",\n                                children: features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"flex items-center gap-x-3 text-base font-semibold leading-7 text-elderly-text\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: \"h-5 w-5 flex-none text-primary-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    feature.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-4 flex flex-auto flex-col text-base leading-7 text-elderly-text-light\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"flex-auto\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, feature.name, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-50 py-24 sm:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl lg:max-w-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight text-elderly-text sm:text-4xl\",\n                                        children: \"Được tin tưởng bởi h\\xe0ng ngh\\xecn người d\\xf9ng\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-4 text-lg leading-8 text-elderly-text-light\",\n                                        children: \"Hệ thống đ\\xe3 gi\\xfap nhiều gia đ\\xecnh chăm s\\xf3c sức khỏe người th\\xe2n hiệu quả\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                className: \"mt-16 grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center sm:grid-cols-2 lg:grid-cols-4\",\n                                children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col bg-white p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-semibold leading-6 text-elderly-text-light\",\n                                                children: stat.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"order-first text-3xl font-bold tracking-tight text-primary-600\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, stat.name, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-24 sm:px-6 sm:py-32 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight text-white sm:text-4xl\",\n                                children: \"Sẵn s\\xe0ng bắt đầu chăm s\\xf3c sức khỏe?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mx-auto mt-6 max-w-xl text-lg leading-8 text-primary-100\",\n                                children: \"Đăng k\\xfd ngay h\\xf4m nay để trải nghiệm hệ thống chăm s\\xf3c sức khỏe th\\xf4ng minh v\\xe0 to\\xe0n diện.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/register\",\n                                        className: \"rounded-lg bg-white px-6 py-3 text-lg font-semibold text-primary-600 shadow-sm hover:bg-primary-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors\",\n                                        children: \"Đăng k\\xfd miễn ph\\xed\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/login\",\n                                        className: \"text-lg font-semibold leading-6 text-white hover:text-primary-100 transition-colors\",\n                                        children: [\n                                            \"Đ\\xe3 c\\xf3 t\\xe0i khoản? Đăng nhập \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                \"aria-hidden\": \"true\",\n                                                children: \"→\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 44\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_c = LandingPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LandingPage);\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Landing/LandingPage.tsx\n"));

/***/ })

});