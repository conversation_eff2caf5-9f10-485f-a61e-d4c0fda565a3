{"name": "elderly-health-frontend", "version": "1.0.0", "description": "Frontend for Elderly Health Support System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@auth0/nextjs-auth0": "^3.5.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.7", "@types/node": "20.10.5", "@types/react": "18.2.45", "@types/react-dom": "18.2.18", "axios": "^1.6.2", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "next": "14.0.4", "react": "18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "swr": "^2.2.4", "tailwind-merge": "^2.2.0", "typescript": "5.3.3", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "autoprefixer": "^10.4.16", "eslint": "8.56.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["elderly", "health", "nextjs", "react", "tailwindcss", "auth0", "healthcare"], "author": "Elderly Health Support Team", "license": "MIT"}