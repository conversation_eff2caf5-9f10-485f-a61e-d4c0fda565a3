/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightOnRectangleIcon: () => (/* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   HeartIcon: () => (/* reexport safe */ _HeartIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   UserCircleIcon: () => (/* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BellIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _HeartIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HeartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLEJhcnMzSWNvbixCZWxsSWNvbixDb2c2VG9vdGhJY29uLEhlYXJ0SWNvbixVc2VyQ2lyY2xlSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUNxRjtBQUNoQztBQUNGO0FBQ1U7QUFDUiIsInNvdXJjZXMiOlsid2VicGFjazovL2VsZGVybHktaGVhbHRoLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/ZjNmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiB9IGZyb20gXCIuL0Fycm93UmlnaHRPblJlY3RhbmdsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJzM0ljb24gfSBmcm9tIFwiLi9CYXJzM0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWxsSWNvbiB9IGZyb20gXCIuL0JlbGxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nNlRvb3RoSWNvbiB9IGZyb20gXCIuL0NvZzZUb290aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIZWFydEljb24gfSBmcm9tIFwiLi9IZWFydEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyQ2lyY2xlSWNvbiB9IGZyb20gXCIuL1VzZXJDaXJjbGVJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChartBarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!******************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BeakerIcon,CalendarIcon,ChartBarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BeakerIcon: () => (/* reexport safe */ _BeakerIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalendarIcon: () => (/* reexport safe */ _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   HeartIcon: () => (/* reexport safe */ _HeartIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _BeakerIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BeakerIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalendarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _HeartIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./HeartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CZWFrZXJJY29uLENhbGVuZGFySWNvbixDaGFydEJhckljb24sSGVhcnRJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDdUQ7QUFDSTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz80NWZmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWFrZXJJY29uIH0gZnJvbSBcIi4vQmVha2VySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGVuZGFySWNvbiB9IGZyb20gXCIuL0NhbGVuZGFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXJ0QmFySWNvbiB9IGZyb20gXCIuL0NoYXJ0QmFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhlYXJ0SWNvbiB9IGZyb20gXCIuL0hlYXJ0SWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChartBarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BeakerIcon: () => (/* reexport safe */ _BeakerIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalendarIcon: () => (/* reexport safe */ _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChatBubbleLeftRightIcon: () => (/* reexport safe */ _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   HeartIcon: () => (/* reexport safe */ _HeartIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   UserIcon: () => (/* reexport safe */ _UserIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _BeakerIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BeakerIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalendarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubbleLeftRightIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _HeartIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HeartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _UserIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UserIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CZWFrZXJJY29uLENhbGVuZGFySWNvbixDaGF0QnViYmxlTGVmdFJpZ2h0SWNvbixDb2c2VG9vdGhJY29uLEhlYXJ0SWNvbixIb21lSWNvbixVc2VySWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3VEO0FBQ0k7QUFDc0I7QUFDcEI7QUFDUjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz8xNGY4Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWFrZXJJY29uIH0gZnJvbSBcIi4vQmVha2VySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGVuZGFySWNvbiB9IGZyb20gXCIuL0NhbGVuZGFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXRCdWJibGVMZWZ0UmlnaHRJY29uIH0gZnJvbSBcIi4vQ2hhdEJ1YmJsZUxlZnRSaWdodEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2c2VG9vdGhJY29uIH0gZnJvbSBcIi4vQ29nNlRvb3RoSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhlYXJ0SWNvbiB9IGZyb20gXCIuL0hlYXJ0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWVJY29uIH0gZnJvbSBcIi4vSG9tZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VySWNvbiB9IGZyb20gXCIuL1VzZXJJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,HeartIcon,ShieldCheckIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!**********************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,HeartIcon,ShieldCheckIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChatBubbleLeftRightIcon: () => (/* reexport safe */ _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ClockIcon: () => (/* reexport safe */ _ClockIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   HeartIcon: () => (/* reexport safe */ _HeartIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   ShieldCheckIcon: () => (/* reexport safe */ _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   UserGroupIcon: () => (/* reexport safe */ _UserGroupIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _ChatBubbleLeftRightIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ChatBubbleLeftRightIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _ClockIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ClockIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _HeartIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./HeartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ShieldCheckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _UserGroupIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./UserGroupIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGFydEJhckljb24sQ2hhdEJ1YmJsZUxlZnRSaWdodEljb24sQ2xvY2tJY29uLEhlYXJ0SWNvbixTaGllbGRDaGVja0ljb24sVXNlckdyb3VwSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUMyRDtBQUNzQjtBQUM1QjtBQUNBO0FBQ1kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGRlcmx5LWhlYWx0aC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzPzE5ZTUiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXJ0QmFySWNvbiB9IGZyb20gXCIuL0NoYXJ0QmFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXRCdWJibGVMZWZ0UmlnaHRJY29uIH0gZnJvbSBcIi4vQ2hhdEJ1YmJsZUxlZnRSaWdodEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDbG9ja0ljb24gfSBmcm9tIFwiLi9DbG9ja0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIZWFydEljb24gfSBmcm9tIFwiLi9IZWFydEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaGllbGRDaGVja0ljb24gfSBmcm9tIFwiLi9TaGllbGRDaGVja0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyR3JvdXBJY29uIH0gZnJvbSBcIi4vVXNlckdyb3VwSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,HeartIcon,ShieldCheckIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js":
/*!***********************************************************************************************************!*\
  !*** __barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Menu: () => (/* reexport safe */ D_CodeThue2025_SucKhoe_frontend_node_modules_headlessui_react_dist_components_menu_menu_js__WEBPACK_IMPORTED_MODULE_0__.Menu),\n/* harmony export */   Transition: () => (/* reexport safe */ D_CodeThue2025_SucKhoe_frontend_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__.Transition)\n/* harmony export */ });\n/* harmony import */ var D_CodeThue2025_SucKhoe_frontend_node_modules_headlessui_react_dist_components_menu_menu_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/menu/menu.js */ \"./node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var D_CodeThue2025_SucKhoe_frontend_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/transitions/transition.js */ \"./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NZW51LFRyYW5zaXRpb24hPSEuL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hlYWRsZXNzdWkuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQzZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9oZWFkbGVzc3VpLmVzbS5qcz9kZTdlIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgTWVudSB9IGZyb20gXCJEOlxcXFxDb2RlVGh1ZTIwMjVcXFxcU3VjS2hvZVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcQGhlYWRsZXNzdWlcXFxccmVhY3RcXFxcZGlzdFxcXFxjb21wb25lbnRzXFxcXG1lbnVcXFxcbWVudS5qc1wiXG5leHBvcnQgeyBUcmFuc2l0aW9uIH0gZnJvbSBcIkQ6XFxcXENvZGVUaHVlMjAyNVxcXFxTdWNLaG9lXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxAaGVhZGxlc3N1aVxcXFxyZWFjdFxcXFxkaXN0XFxcXGNvbXBvbmVudHNcXFxcdHJhbnNpdGlvbnNcXFxcdHJhbnNpdGlvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!./node_modules/date-fns/esm/index.js":
/*!**********************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!./node_modules/date-fns/esm/index.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addDays: () => (/* reexport safe */ _addDays_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   differenceInYears: () => (/* reexport safe */ _differenceInYears_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   endOfDay: () => (/* reexport safe */ _endOfDay_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   format: () => (/* reexport safe */ _format_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   isValid: () => (/* reexport safe */ _isValid_index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   parseISO: () => (/* reexport safe */ _parseISO_index_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   startOfDay: () => (/* reexport safe */ _startOfDay_index_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _addDays_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./addDays/index.js */ \"./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _differenceInYears_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./differenceInYears/index.js */ \"./node_modules/date-fns/esm/differenceInYears/index.js\");\n/* harmony import */ var _endOfDay_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./endOfDay/index.js */ \"./node_modules/date-fns/esm/endOfDay/index.js\");\n/* harmony import */ var _format_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./format/index.js */ \"./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _isValid_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./isValid/index.js */ \"./node_modules/date-fns/esm/isValid/index.js\");\n/* harmony import */ var _parseISO_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./parseISO/index.js */ \"./node_modules/date-fns/esm/parseISO/index.js\");\n/* harmony import */ var _startOfDay_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./startOfDay/index.js */ \"./node_modules/date-fns/esm/startOfDay/index.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1hZGREYXlzLGRpZmZlcmVuY2VJblllYXJzLGVuZE9mRGF5LGZvcm1hdCxpc1ZhbGlkLHBhcnNlSVNPLHN0YXJ0T2ZEYXkhPSEuL25vZGVfbW9kdWxlcy9kYXRlLWZucy9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3VEO0FBQ29CO0FBQ2xCO0FBQ0o7QUFDRTtBQUNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZGF0ZS1mbnMvZXNtL2luZGV4LmpzP2JkN2EiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIGFkZERheXMgfSBmcm9tIFwiLi9hZGREYXlzL2luZGV4LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgZGlmZmVyZW5jZUluWWVhcnMgfSBmcm9tIFwiLi9kaWZmZXJlbmNlSW5ZZWFycy9pbmRleC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIGVuZE9mRGF5IH0gZnJvbSBcIi4vZW5kT2ZEYXkvaW5kZXguanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBmb3JtYXQgfSBmcm9tIFwiLi9mb3JtYXQvaW5kZXguanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBpc1ZhbGlkIH0gZnJvbSBcIi4vaXNWYWxpZC9pbmRleC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIHBhcnNlSVNPIH0gZnJvbSBcIi4vcGFyc2VJU08vaW5kZXguanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBzdGFydE9mRGF5IH0gZnJvbSBcIi4vc3RhcnRPZkRheS9pbmRleC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!./node_modules/date-fns/esm/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Dashboard/Dashboard.tsx":
/*!********************************************!*\
  !*** ./components/Dashboard/Dashboard.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,CalendarIcon,ChartBarIcon,HeartIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChartBarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_api__WEBPACK_IMPORTED_MODULE_3__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_api__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst Dashboard = ()=>{\n    const { user } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        healthRecords: 0,\n        activeMedications: 0,\n        upcomingSchedules: 0,\n        weeklyReports: 0\n    });\n    const [todayReminders, setTodayReminders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            // Load dashboard stats in parallel\n            const [healthRecords, medications, schedules] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.healthApi.getRecords({\n                    limit: 100\n                }).catch(()=>[]),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.medicationApi.getMedications(true).catch(()=>[]),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.scheduleApi.getSchedules({\n                    upcoming_only: true,\n                    limit: 10\n                }).catch(()=>[])\n            ]);\n            // Calculate stats\n            const now = new Date();\n            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n            const weeklyHealthRecords = healthRecords.filter((record)=>new Date(record.recorded_at) >= weekAgo);\n            setStats({\n                healthRecords: healthRecords.length,\n                activeMedications: medications.length,\n                upcomingSchedules: schedules.length,\n                weeklyReports: weeklyHealthRecords.length\n            });\n            // Load today's reminders\n            const today = new Date().toISOString().split(\"T\")[0];\n            const todaySchedules = schedules.filter((schedule)=>schedule.scheduled_at.startsWith(today));\n            const reminders = [\n                ...todaySchedules.map((schedule)=>({\n                        id: schedule.id,\n                        title: schedule.title,\n                        time: new Date(schedule.scheduled_at).toLocaleTimeString(\"vi-VN\", {\n                            hour: \"2-digit\",\n                            minute: \"2-digit\"\n                        }),\n                        type: \"appointment\",\n                        color: \"bg-blue-50 border-blue-200 text-blue-800\"\n                    })),\n                // Add medication reminders (simplified - could be enhanced)\n                ...medications.slice(0, 2).map((med, index)=>({\n                        id: `med-${med.id}`,\n                        title: `Uống ${med.name}`,\n                        time: index === 0 ? \"8:00\" : \"20:00\",\n                        type: \"medication\",\n                        color: \"bg-yellow-50 border-yellow-200 text-yellow-800\"\n                    }))\n            ];\n            setTodayReminders(reminders.slice(0, 3)); // Limit to 3 reminders\n        } catch (err) {\n            console.error(\"Error loading dashboard data:\", err);\n            setError(\"Kh\\xf4ng thể tải dữ liệu dashboard\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const statsConfig = [\n        {\n            name: \"Chỉ số sức khỏe\",\n            value: stats.healthRecords.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HeartIcon,\n            color: \"text-red-600 bg-red-100\"\n        },\n        {\n            name: \"Thuốc đang d\\xf9ng\",\n            value: stats.activeMedications.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.BeakerIcon,\n            color: \"text-blue-600 bg-blue-100\"\n        },\n        {\n            name: \"Lịch hẹn sắp tới\",\n            value: stats.upcomingSchedules.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CalendarIcon,\n            color: \"text-green-600 bg-green-100\"\n        },\n        {\n            name: \"B\\xe1o c\\xe1o tuần n\\xe0y\",\n            value: stats.weeklyReports.toString(),\n            icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChartBarIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChartBarIcon,\n            color: \"text-purple-600 bg-purple-100\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-elderly-text\",\n                        children: [\n                            \"Xin ch\\xe0o, \",\n                            user?.full_name || \"Bạn\",\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-elderly-text-light mt-2\",\n                        children: \"Ch\\xe0o mừng bạn đến với hệ thống chăm s\\xf3c sức khỏe\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadDashboardData,\n                                className: \"mt-2 text-red-600 hover:text-red-800 text-sm underline\",\n                                children: \"Thử lại\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: statsConfig.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 rounded-lg ${stat.color}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-elderly-text\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elderly-text-light\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined)\n                    }, stat.name, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-elderly-text mb-4\",\n                                children: \"H\\xe0nh động nhanh\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/health\",\n                                        className: \"w-full btn btn-primary text-left\",\n                                        children: \"Ghi nhận chỉ số sức khỏe\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/schedules\",\n                                        className: \"w-full btn btn-secondary text-left\",\n                                        children: \"Đặt lịch nhắc nhở\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/chat\",\n                                        className: \"w-full btn btn-secondary text-left\",\n                                        children: \"Tư vấn với AI\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-elderly-text mb-4\",\n                                children: \"Nhắc nhở h\\xf4m nay\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: todayReminders.length > 0 ? todayReminders.map((reminder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `p-3 border rounded-lg ${reminder.color}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: reminder.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm opacity-75\",\n                                                children: reminder.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, reminder.id, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gray-50 border border-gray-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-center\",\n                                        children: \"Kh\\xf4ng c\\xf3 nhắc nhở n\\xe0o h\\xf4m nay\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Dashboard\\\\Dashboard.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Dashboard/Dashboard.tsx\n");

/***/ }),

/***/ "./components/Landing/LandingPage.tsx":
/*!********************************************!*\
  !*** ./components/Landing/LandingPage.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,HeartIcon,ShieldCheckIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,HeartIcon,ShieldCheckIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/**\n * Landing page component for non-authenticated users\n */ \n\n\n\nconst features = [\n    {\n        name: \"Theo d\\xf5i sức khỏe\",\n        description: \"Ghi nhận v\\xe0 theo d\\xf5i c\\xe1c chỉ số sức khỏe quan trọng như huyết \\xe1p, đường huyết, nhịp tim.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.HeartIcon\n    },\n    {\n        name: \"Biểu đồ trực quan\",\n        description: \"Xem biểu đồ thay đổi sức khỏe theo thời gian để hiểu r\\xf5 t\\xecnh trạng của bạn.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChartBarIcon\n    },\n    {\n        name: \"Nhắc nhở th\\xf4ng minh\",\n        description: \"Nhận nhắc nhở uống thuốc v\\xe0 lịch kh\\xe1m bệnh đ\\xfang giờ, kh\\xf4ng bao giờ qu\\xean.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ClockIcon\n    },\n    {\n        name: \"Tư vấn AI\",\n        description: \"Chatbot AI th\\xf4ng minh tư vấn sức khỏe 24/7, lu\\xf4n sẵn s\\xe0ng hỗ trợ bạn.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ChatBubbleLeftRightIcon\n    },\n    {\n        name: \"Bảo mật cao\",\n        description: \"Th\\xf4ng tin sức khỏe được bảo vệ an to\\xe0n với c\\xf4ng nghệ m\\xe3 h\\xf3a ti\\xean tiến.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ShieldCheckIcon\n    },\n    {\n        name: \"Dễ sử dụng\",\n        description: \"Giao diện th\\xe2n thiện, ph\\xf9 hợp với người cao tuổi, dễ d\\xe0ng sử dụng.\",\n        icon: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_ClockIcon_HeartIcon_ShieldCheckIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.UserGroupIcon\n    }\n];\nconst stats = [\n    {\n        name: \"Người d\\xf9ng tin tưởng\",\n        value: \"1,000+\"\n    },\n    {\n        name: \"Chỉ số sức khỏe được theo d\\xf5i\",\n        value: \"50,000+\"\n    },\n    {\n        name: \"Lời nhắc đ\\xe3 gửi\",\n        value: \"100,000+\"\n    },\n    {\n        name: \"Tư vấn AI\",\n        value: \"24/7\"\n    }\n];\nconst LandingPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative isolate px-6 pt-14 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary-400 to-primary-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-4xl py-32 sm:py-48 lg:py-56\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold tracking-tight text-elderly-text sm:text-6xl\",\n                                    children: [\n                                        \"Chăm s\\xf3c sức khỏe\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-600\",\n                                            children: \"người cao tuổi\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" th\\xf4ng minh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-lg leading-8 text-elderly-text-light max-w-2xl mx-auto\",\n                                    children: \"Hệ thống hỗ trợ theo d\\xf5i v\\xe0 chăm s\\xf3c sức khỏe to\\xe0n diện, gi\\xfap người cao tuổi v\\xe0 gia đ\\xecnh quản l\\xfd sức khỏe một c\\xe1ch dễ d\\xe0ng v\\xe0 hiệu quả.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/register\",\n                                            className: \"btn btn-primary btn-lg\",\n                                            children: \"Đăng k\\xfd miễn ph\\xed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/login\",\n                                            className: \"btn btn-outline btn-lg\",\n                                            children: \"Đăng nhập\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary-400 to-primary-600 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"features\",\n                className: \"py-24 sm:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl lg:text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-base font-semibold leading-7 text-primary-600\",\n                                    children: \"T\\xednh năng nổi bật\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-3xl font-bold tracking-tight text-elderly-text sm:text-4xl\",\n                                    children: \"Mọi thứ bạn cần để chăm s\\xf3c sức khỏe\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-lg leading-8 text-elderly-text-light\",\n                                    children: \"Hệ thống được thiết kế đặc biệt cho người cao tuổi với giao diện đơn giản, t\\xednh năng to\\xe0n diện v\\xe0 hỗ trợ 24/7.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                className: \"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\",\n                                children: features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"flex items-center gap-x-3 text-base font-semibold leading-7 text-elderly-text\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: \"h-5 w-5 flex-none text-primary-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    feature.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-4 flex flex-auto flex-col text-base leading-7 text-elderly-text-light\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"flex-auto\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, feature.name, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-50 py-24 sm:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl lg:max-w-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight text-elderly-text sm:text-4xl\",\n                                        children: \"Được tin tưởng bởi h\\xe0ng ngh\\xecn người d\\xf9ng\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-4 text-lg leading-8 text-elderly-text-light\",\n                                        children: \"Hệ thống đ\\xe3 gi\\xfap nhiều gia đ\\xecnh chăm s\\xf3c sức khỏe người th\\xe2n hiệu quả\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                className: \"mt-16 grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center sm:grid-cols-2 lg:grid-cols-4\",\n                                children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col bg-white p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-semibold leading-6 text-elderly-text-light\",\n                                                children: stat.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"order-first text-3xl font-bold tracking-tight text-primary-600\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, stat.name, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-24 sm:px-6 sm:py-32 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-2xl text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight text-white sm:text-4xl\",\n                                children: \"Sẵn s\\xe0ng bắt đầu chăm s\\xf3c sức khỏe?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mx-auto mt-6 max-w-xl text-lg leading-8 text-primary-100\",\n                                children: \"Đăng k\\xfd ngay h\\xf4m nay để trải nghiệm hệ thống chăm s\\xf3c sức khỏe th\\xf4ng minh v\\xe0 to\\xe0n diện.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/register\",\n                                        className: \"rounded-lg bg-white px-6 py-3 text-lg font-semibold text-primary-600 shadow-sm hover:bg-primary-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transition-colors\",\n                                        children: \"Đăng k\\xfd miễn ph\\xed\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/login\",\n                                        className: \"text-lg font-semibold leading-6 text-white hover:text-primary-100 transition-colors\",\n                                        children: [\n                                            \"Đ\\xe3 c\\xf3 t\\xe0i khoản? Đăng nhập \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                \"aria-hidden\": \"true\",\n                                                children: \"→\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 44\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Landing\\\\LandingPage.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Landing/LandingPage.tsx\n");

/***/ }),

/***/ "./components/Layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/Layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Footer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-elderly-border mt-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-elderly-text mb-4\",\n                                        children: \"Hệ thống SứcKhỏe\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 11,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elderly-text-light\",\n                                        children: \"Chăm s\\xf3c sức khỏe người cao tuổi với c\\xf4ng nghệ hiện đại v\\xe0 t\\xecnh y\\xeau thương.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-elderly-text mb-4\",\n                                        children: \"Li\\xean kết hữu \\xedch\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/about\",\n                                                    className: \"text-elderly-text-light hover:text-primary-600\",\n                                                    children: \"Về ch\\xfang t\\xf4i\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/privacy\",\n                                                    className: \"text-elderly-text-light hover:text-primary-600\",\n                                                    children: \"Ch\\xednh s\\xe1ch bảo mật\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 30,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/terms\",\n                                                    className: \"text-elderly-text-light hover:text-primary-600\",\n                                                    children: \"Điều khoản sử dụng\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-elderly-text mb-4\",\n                                        children: \"Hỗ trợ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-elderly-text-light\",\n                                                    children: \"Hotline: 1900-1234\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-elderly-text-light\",\n                                                    children: \"Email: <EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 pt-8 border-t border-elderly-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-elderly-text-light\",\n                            children: \"\\xa9 2024 Hệ thống hỗ trợ sức khỏe người cao tuổi. Tất cả quyền được bảo lưu.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout/Footer.tsx\n");

/***/ }),

/***/ "./components/Layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/Layout/Header.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,Cog6ToothIcon,HeartIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * Header component for Elderly Health Support System\n */ \n\n\n\n\n\n\nconst Header = ({ onMenuClick })=>{\n    const { user, isLoading, logout } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [notificationsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3); // This would come from API\n    const navigation = [\n        {\n            name: \"Trang chủ\",\n            href: \"/\",\n            current: false\n        },\n        {\n            name: \"Sức khỏe\",\n            href: \"/health\",\n            current: false\n        },\n        {\n            name: \"Thuốc\",\n            href: \"/medications\",\n            current: false\n        },\n        {\n            name: \"Lịch hẹn\",\n            href: \"/schedules\",\n            current: false\n        },\n        {\n            name: \"Tư vấn AI\",\n            href: \"/chat\",\n            current: false\n        }\n    ];\n    const userNavigation = [\n        {\n            name: \"Hồ sơ c\\xe1 nh\\xe2n\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserCircleIcon\n        },\n        {\n            name: \"C\\xe0i đặt\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.Cog6ToothIcon\n        }\n    ];\n    const handleLogout = ()=>{\n        logout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-elderly-border sticky top-0 z-40\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"lg:hidden -ml-2 mr-2 p-2 rounded-md text-elderly-text hover:bg-elderly-hover-bg focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                    onClick: onMenuClick,\n                                    \"aria-label\": \"Mở menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.Bars3Icon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.HeartIcon, {\n                                                className: \"h-8 w-8 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-xl font-bold text-elderly-text hidden sm:block\",\n                                                children: \"SứcKhỏe\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:ml-8 lg:flex lg:space-x-1\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"nav-link\", item.current ? \"nav-link-active\" : \"nav-link-inactive\"),\n                                            children: item.name\n                                        }, item.name, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"relative p-2 text-elderly-text hover:bg-elderly-hover-bg rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                        \"aria-label\": \"Th\\xf4ng b\\xe1o\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.BellIcon, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            notificationsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                children: notificationsCount > 9 ? \"9+\" : notificationsCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                                        as: \"div\",\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Button, {\n                                                className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-elderly-hover-bg focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserCircleIcon, {\n                                                            className: \"h-8 w-8 text-elderly-text-light\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden md:block text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-elderly-text\",\n                                                                children: user.full_name || \"Người d\\xf9ng\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-elderly-text-light\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Transition, {\n                                                enter: \"transition ease-out duration-100\",\n                                                enterFrom: \"transform opacity-0 scale-95\",\n                                                enterTo: \"transform opacity-100 scale-100\",\n                                                leave: \"transition ease-in duration-75\",\n                                                leaveFrom: \"transform opacity-100 scale-100\",\n                                                leaveTo: \"transform opacity-0 scale-95\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Items, {\n                                                    className: \"absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-1\",\n                                                        children: [\n                                                            userNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                            href: item.href,\n                                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center px-4 py-3 text-sm\", active ? \"bg-elderly-hover-bg text-elderly-text\" : \"text-elderly-text-light\"),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                                    className: \"mr-3 h-5 w-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                                    lineNumber: 144,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                item.name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                            lineNumber: 135,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                }, item.name, false, {\n                                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 27\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                                children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleLogout,\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center w-full px-4 py-3 text-sm text-left\", active ? \"bg-elderly-hover-bg text-elderly-text\" : \"text-elderly-text-light\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_Cog6ToothIcon_HeartIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ArrowRightOnRectangleIcon, {\n                                                                                className: \"mr-3 h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                                lineNumber: 161,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            \"Đăng xuất\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                        lineNumber: 152,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/auth/login\",\n                                        className: \"text-elderly-text hover:text-primary-600 font-medium transition-colors\",\n                                        children: \"Đăng nhập\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/auth/register\",\n                                        className: \"btn btn-primary\",\n                                        children: \"Đăng k\\xfd\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden border-t border-elderly-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"px-4 py-2 space-y-1\",\n                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"block px-3 py-2 rounded-md text-base font-medium\", item.current ? \"bg-primary-100 text-primary-700\" : \"text-elderly-text hover:bg-elderly-hover-bg\"),\n                            children: item.name\n                        }, item.name, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xheW91dC9IZWFkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7O0NBRUM7QUFFdUM7QUFDSDtBQUNSO0FBRXdCO0FBUWhCO0FBQ0o7QUFNakMsTUFBTWEsU0FBZ0MsQ0FBQyxFQUFFQyxXQUFXLEVBQUU7SUFDcEQsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsTUFBTSxFQUFFLEdBQUdmLGtEQUFPQTtJQUMzQyxNQUFNLENBQUNnQixtQkFBbUIsR0FBR2pCLCtDQUFRQSxDQUFDLElBQUksMkJBQTJCO0lBRXJFLE1BQU1rQixhQUFhO1FBQ2pCO1lBQUVDLE1BQU07WUFBYUMsTUFBTTtZQUFLQyxTQUFTO1FBQU07UUFDL0M7WUFBRUYsTUFBTTtZQUFZQyxNQUFNO1lBQVdDLFNBQVM7UUFBTTtRQUNwRDtZQUFFRixNQUFNO1lBQVNDLE1BQU07WUFBZ0JDLFNBQVM7UUFBTTtRQUN0RDtZQUFFRixNQUFNO1lBQVlDLE1BQU07WUFBY0MsU0FBUztRQUFNO1FBQ3ZEO1lBQUVGLE1BQU07WUFBYUMsTUFBTTtZQUFTQyxTQUFTO1FBQU07S0FDcEQ7SUFFRCxNQUFNQyxpQkFBaUI7UUFDckI7WUFBRUgsTUFBTTtZQUFpQkMsTUFBTTtZQUFZRyxNQUFNaEIsaUxBQWNBO1FBQUM7UUFDaEU7WUFBRVksTUFBTTtZQUFXQyxNQUFNO1lBQWFHLE1BQU1mLGdMQUFhQTtRQUFDO0tBQzNEO0lBRUQsTUFBTWdCLGVBQWU7UUFDbkJSO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ1M7UUFBT0MsV0FBVTs7MEJBQ2hCLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUViLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBRWIsOERBQUNFO29DQUNDQyxNQUFLO29DQUNMSCxXQUFVO29DQUNWSSxTQUFTakI7b0NBQ1RrQixjQUFXOzhDQUVYLDRFQUFDMUIsNEtBQVNBO3dDQUFDcUIsV0FBVTs7Ozs7Ozs7Ozs7OENBSXZCLDhEQUFDeEIsa0RBQUlBO29DQUFDa0IsTUFBSztvQ0FBSU0sV0FBVTs4Q0FDdkIsNEVBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ2hCLDRLQUFTQTtnREFBQ2dCLFdBQVU7Ozs7OzswREFDckIsOERBQUNNO2dEQUFLTixXQUFVOzBEQUEyRDs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTy9FLDhEQUFDTztvQ0FBSVAsV0FBVTs4Q0FDWlIsV0FBV2dCLEdBQUcsQ0FBQyxDQUFDQyxxQkFDZiw4REFBQ2pDLGtEQUFJQTs0Q0FFSGtCLE1BQU1lLEtBQUtmLElBQUk7NENBQ2ZNLFdBQVdmLDhDQUFFQSxDQUNYLFlBQ0F3QixLQUFLZCxPQUFPLEdBQUcsb0JBQW9CO3NEQUdwQ2MsS0FBS2hCLElBQUk7MkNBUExnQixLQUFLaEIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FjdEIsOERBQUNROzRCQUFJRCxXQUFVO3NDQUNaWixxQkFDQzs7a0RBRUUsOERBQUNjO3dDQUNDQyxNQUFLO3dDQUNMSCxXQUFVO3dDQUNWSyxjQUFXOzswREFFWCw4REFBQ3pCLDJLQUFRQTtnREFBQ29CLFdBQVU7Ozs7Ozs0Q0FDbkJULHFCQUFxQixtQkFDcEIsOERBQUNlO2dEQUFLTixXQUFVOzBEQUNiVCxxQkFBcUIsSUFBSSxPQUFPQTs7Ozs7Ozs7Ozs7O2tEQU12Qyw4REFBQ2QseUZBQUlBO3dDQUFDaUMsSUFBRzt3Q0FBTVYsV0FBVTs7MERBQ3ZCLDhEQUFDdkIseUZBQUlBLENBQUNrQyxNQUFNO2dEQUFDWCxXQUFVOztrRUFDckIsOERBQUNDO3dEQUFJRCxXQUFVO2tFQUNiLDRFQUFDbkIsaUxBQWNBOzREQUFDbUIsV0FBVTs7Ozs7Ozs7Ozs7a0VBRTVCLDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNZO2dFQUFFWixXQUFVOzBFQUNWWixLQUFLeUIsU0FBUyxJQUFJOzs7Ozs7MEVBRXJCLDhEQUFDRDtnRUFBRVosV0FBVTswRUFDVlosS0FBSzBCLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLakIsOERBQUNwQywrRkFBVUE7Z0RBQ1RxQyxPQUFNO2dEQUNOQyxXQUFVO2dEQUNWQyxTQUFRO2dEQUNSQyxPQUFNO2dEQUNOQyxXQUFVO2dEQUNWQyxTQUFROzBEQUVSLDRFQUFDM0MseUZBQUlBLENBQUM0QyxLQUFLO29EQUFDckIsV0FBVTs4REFDcEIsNEVBQUNDO3dEQUFJRCxXQUFVOzs0REFDWkosZUFBZVksR0FBRyxDQUFDLENBQUNDLHFCQUNuQiw4REFBQ2hDLHlGQUFJQSxDQUFDNkMsSUFBSTs4RUFDUCxDQUFDLEVBQUVDLE1BQU0sRUFBRSxpQkFDViw4REFBQy9DLGtEQUFJQTs0RUFDSGtCLE1BQU1lLEtBQUtmLElBQUk7NEVBQ2ZNLFdBQVdmLDhDQUFFQSxDQUNYLHVDQUNBc0MsU0FDSSwwQ0FDQTs7OEZBR04sOERBQUNkLEtBQUtaLElBQUk7b0ZBQUNHLFdBQVU7Ozs7OztnRkFDcEJTLEtBQUtoQixJQUFJOzs7Ozs7O21FQVpBZ0IsS0FBS2hCLElBQUk7Ozs7OzBFQWlCM0IsOERBQUNoQix5RkFBSUEsQ0FBQzZDLElBQUk7MEVBQ1AsQ0FBQyxFQUFFQyxNQUFNLEVBQUUsaUJBQ1YsOERBQUNyQjt3RUFDQ0UsU0FBU047d0VBQ1RFLFdBQVdmLDhDQUFFQSxDQUNYLHdEQUNBc0MsU0FDSSwwQ0FDQTs7MEZBR04sOERBQUN4Qyw0TEFBeUJBO2dGQUFDaUIsV0FBVTs7Ozs7OzRFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0NBV3RFLENBQUNYLDJCQUNDLDhEQUFDWTtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUN4QixrREFBSUE7d0NBQ0hrQixNQUFLO3dDQUNMTSxXQUFVO2tEQUNYOzs7Ozs7a0RBR0QsOERBQUN4QixrREFBSUE7d0NBQUNrQixNQUFLO3dDQUFpQk0sV0FBVTtrREFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBV3BFLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ087b0JBQUlQLFdBQVU7OEJBQ1pSLFdBQVdnQixHQUFHLENBQUMsQ0FBQ0MscUJBQ2YsOERBQUNqQyxrREFBSUE7NEJBRUhrQixNQUFNZSxLQUFLZixJQUFJOzRCQUNmTSxXQUFXZiw4Q0FBRUEsQ0FDWCxvREFDQXdCLEtBQUtkLE9BQU8sR0FDUixvQ0FDQTtzQ0FHTGMsS0FBS2hCLElBQUk7MkJBVExnQixLQUFLaEIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBZ0I1QjtBQUVBLGlFQUFlUCxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9jb21wb25lbnRzL0xheW91dC9IZWFkZXIudHN4PzBjNGYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBIZWFkZXIgY29tcG9uZW50IGZvciBFbGRlcmx5IEhlYWx0aCBTdXBwb3J0IFN5c3RlbVxuICovXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gXCJAL2xpYi9hdXRoXCI7XG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcbmltcG9ydCB7IE1lbnUsIFRyYW5zaXRpb24gfSBmcm9tIFwiQGhlYWRsZXNzdWkvcmVhY3RcIjtcbmltcG9ydCB7XG4gIEJhcnMzSWNvbixcbiAgQmVsbEljb24sXG4gIFVzZXJDaXJjbGVJY29uLFxuICBDb2c2VG9vdGhJY29uLFxuICBBcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLFxuICBIZWFydEljb24sXG59IGZyb20gXCJAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmVcIjtcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XG5cbmludGVyZmFjZSBIZWFkZXJQcm9wcyB7XG4gIG9uTWVudUNsaWNrPzogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgSGVhZGVyOiBSZWFjdC5GQzxIZWFkZXJQcm9wcz4gPSAoeyBvbk1lbnVDbGljayB9KSA9PiB7XG4gIGNvbnN0IHsgdXNlciwgaXNMb2FkaW5nLCBsb2dvdXQgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgW25vdGlmaWNhdGlvbnNDb3VudF0gPSB1c2VTdGF0ZSgzKTsgLy8gVGhpcyB3b3VsZCBjb21lIGZyb20gQVBJXG5cbiAgY29uc3QgbmF2aWdhdGlvbiA9IFtcbiAgICB7IG5hbWU6IFwiVHJhbmcgY2jhu6dcIiwgaHJlZjogXCIvXCIsIGN1cnJlbnQ6IGZhbHNlIH0sXG4gICAgeyBuYW1lOiBcIlPhu6ljIGto4buPZVwiLCBocmVmOiBcIi9oZWFsdGhcIiwgY3VycmVudDogZmFsc2UgfSxcbiAgICB7IG5hbWU6IFwiVGh14buRY1wiLCBocmVmOiBcIi9tZWRpY2F0aW9uc1wiLCBjdXJyZW50OiBmYWxzZSB9LFxuICAgIHsgbmFtZTogXCJM4buLY2ggaOG6uW5cIiwgaHJlZjogXCIvc2NoZWR1bGVzXCIsIGN1cnJlbnQ6IGZhbHNlIH0sXG4gICAgeyBuYW1lOiBcIlTGsCB24bqlbiBBSVwiLCBocmVmOiBcIi9jaGF0XCIsIGN1cnJlbnQ6IGZhbHNlIH0sXG4gIF07XG5cbiAgY29uc3QgdXNlck5hdmlnYXRpb24gPSBbXG4gICAgeyBuYW1lOiBcIkjhu5Mgc8ahIGPDoSBuaMOiblwiLCBocmVmOiBcIi9wcm9maWxlXCIsIGljb246IFVzZXJDaXJjbGVJY29uIH0sXG4gICAgeyBuYW1lOiBcIkPDoGkgxJHhurd0XCIsIGhyZWY6IFwiL3NldHRpbmdzXCIsIGljb246IENvZzZUb290aEljb24gfSxcbiAgXTtcblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSAoKSA9PiB7XG4gICAgbG9nb3V0KCk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1zbSBib3JkZXItYiBib3JkZXItZWxkZXJseS1ib3JkZXIgc3RpY2t5IHRvcC0wIHotNDBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy03eGwgcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtMTYganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIHsvKiBMb2dvIGFuZCBicmFuZCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICB7LyogTW9iaWxlIG1lbnUgYnV0dG9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGc6aGlkZGVuIC1tbC0yIG1yLTIgcC0yIHJvdW5kZWQtbWQgdGV4dC1lbGRlcmx5LXRleHQgaG92ZXI6YmctZWxkZXJseS1ob3Zlci1iZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDBcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtvbk1lbnVDbGlja31cbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIk3hu58gbWVudVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxCYXJzM0ljb24gY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgey8qIExvZ28gKi99XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxIZWFydEljb24gY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LXByaW1hcnktNjAwXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQteGwgZm9udC1ib2xkIHRleHQtZWxkZXJseS10ZXh0IGhpZGRlbiBzbTpibG9ja1wiPlxuICAgICAgICAgICAgICAgICAgU+G7qWNLaOG7j2VcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9MaW5rPlxuXG4gICAgICAgICAgICB7LyogRGVza3RvcCBuYXZpZ2F0aW9uICovfVxuICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6bWwtOCBsZzpmbGV4IGxnOnNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICB7bmF2aWdhdGlvbi5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICBcIm5hdi1saW5rXCIsXG4gICAgICAgICAgICAgICAgICAgIGl0ZW0uY3VycmVudCA/IFwibmF2LWxpbmstYWN0aXZlXCIgOiBcIm5hdi1saW5rLWluYWN0aXZlXCJcbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9uYXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUmlnaHQgc2lkZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAge3VzZXIgPyAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgey8qIE5vdGlmaWNhdGlvbnMgKi99XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBwLTIgdGV4dC1lbGRlcmx5LXRleHQgaG92ZXI6YmctZWxkZXJseS1ob3Zlci1iZyByb3VuZGVkLWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LTUwMFwiXG4gICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiVGjDtG5nIGLDoW9cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxCZWxsSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz5cbiAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb25zQ291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0xIGgtNSB3LTUgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAge25vdGlmaWNhdGlvbnNDb3VudCA+IDkgPyBcIjkrXCIgOiBub3RpZmljYXRpb25zQ291bnR9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICB7LyogVXNlciBtZW51ICovfVxuICAgICAgICAgICAgICAgIDxNZW51IGFzPVwiZGl2XCIgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgIDxNZW51LkJ1dHRvbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgcC0yIHJvdW5kZWQtbGcgaG92ZXI6YmctZWxkZXJseS1ob3Zlci1iZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFVzZXJDaXJjbGVJY29uIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1lbGRlcmx5LXRleHQtbGlnaHRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2sgdGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWVsZGVybHktdGV4dFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3VzZXIuZnVsbF9uYW1lIHx8IFwiTmfGsOG7nWkgZMO5bmdcIn1cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWVsZGVybHktdGV4dC1saWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3VzZXIuZW1haWx9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvTWVudS5CdXR0b24+XG5cbiAgICAgICAgICAgICAgICAgIDxUcmFuc2l0aW9uXG4gICAgICAgICAgICAgICAgICAgIGVudGVyPVwidHJhbnNpdGlvbiBlYXNlLW91dCBkdXJhdGlvbi0xMDBcIlxuICAgICAgICAgICAgICAgICAgICBlbnRlckZyb209XCJ0cmFuc2Zvcm0gb3BhY2l0eS0wIHNjYWxlLTk1XCJcbiAgICAgICAgICAgICAgICAgICAgZW50ZXJUbz1cInRyYW5zZm9ybSBvcGFjaXR5LTEwMCBzY2FsZS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICBsZWF2ZT1cInRyYW5zaXRpb24gZWFzZS1pbiBkdXJhdGlvbi03NVwiXG4gICAgICAgICAgICAgICAgICAgIGxlYXZlRnJvbT1cInRyYW5zZm9ybSBvcGFjaXR5LTEwMCBzY2FsZS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICBsZWF2ZVRvPVwidHJhbnNmb3JtIG9wYWNpdHktMCBzY2FsZS05NVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxNZW51Lkl0ZW1zIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTAgei0xMCBtdC0yIHctNTYgb3JpZ2luLXRvcC1yaWdodCByb3VuZGVkLWxnIGJnLXdoaXRlIHNoYWRvdy1sZyByaW5nLTEgcmluZy1ibGFjayByaW5nLW9wYWNpdHktNSBmb2N1czpvdXRsaW5lLW5vbmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyTmF2aWdhdGlvbi5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnUuSXRlbSBrZXk9e2l0ZW0ubmFtZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeyh7IGFjdGl2ZSB9KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTMgdGV4dC1zbVwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWVsZGVybHktaG92ZXItYmcgdGV4dC1lbGRlcmx5LXRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZWxkZXJseS10ZXh0LWxpZ2h0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGl0ZW0uaWNvbiBjbGFzc05hbWU9XCJtci0zIGgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudS5JdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8TWVudS5JdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7KHsgYWN0aXZlIH0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVMb2dvdXR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIHctZnVsbCBweC00IHB5LTMgdGV4dC1zbSB0ZXh0LWxlZnRcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWVsZGVybHktaG92ZXItYmcgdGV4dC1lbGRlcmx5LXRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWVsZGVybHktdGV4dC1saWdodFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uIGNsYXNzTmFtZT1cIm1yLTMgaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDEkMSDbmcgeHXhuqV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L01lbnUuSXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9NZW51Lkl0ZW1zPlxuICAgICAgICAgICAgICAgICAgPC9UcmFuc2l0aW9uPlxuICAgICAgICAgICAgICAgIDwvTWVudT5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAhaXNMb2FkaW5nICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9hdXRoL2xvZ2luXCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1lbGRlcmx5LXRleHQgaG92ZXI6dGV4dC1wcmltYXJ5LTYwMCBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIMSQxINuZyBuaOG6rXBcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYXV0aC9yZWdpc3RlclwiIGNsYXNzTmFtZT1cImJ0biBidG4tcHJpbWFyeVwiPlxuICAgICAgICAgICAgICAgICAgICDEkMSDbmcga8O9XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIClcbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNb2JpbGUgbmF2aWdhdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIGJvcmRlci10IGJvcmRlci1lbGRlcmx5LWJvcmRlclwiPlxuICAgICAgICA8bmF2IGNsYXNzTmFtZT1cInB4LTQgcHktMiBzcGFjZS15LTFcIj5cbiAgICAgICAgICB7bmF2aWdhdGlvbi5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxuICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgXCJibG9jayBweC0zIHB5LTIgcm91bmRlZC1tZCB0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIixcbiAgICAgICAgICAgICAgICBpdGVtLmN1cnJlbnRcbiAgICAgICAgICAgICAgICAgID8gXCJiZy1wcmltYXJ5LTEwMCB0ZXh0LXByaW1hcnktNzAwXCJcbiAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWVsZGVybHktdGV4dCBob3ZlcjpiZy1lbGRlcmx5LWhvdmVyLWJnXCJcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9uYXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2hlYWRlcj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEhlYWRlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlQXV0aCIsIkxpbmsiLCJNZW51IiwiVHJhbnNpdGlvbiIsIkJhcnMzSWNvbiIsIkJlbGxJY29uIiwiVXNlckNpcmNsZUljb24iLCJDb2c2VG9vdGhJY29uIiwiQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiIsIkhlYXJ0SWNvbiIsImNuIiwiSGVhZGVyIiwib25NZW51Q2xpY2siLCJ1c2VyIiwiaXNMb2FkaW5nIiwibG9nb3V0Iiwibm90aWZpY2F0aW9uc0NvdW50IiwibmF2aWdhdGlvbiIsIm5hbWUiLCJocmVmIiwiY3VycmVudCIsInVzZXJOYXZpZ2F0aW9uIiwiaWNvbiIsImhhbmRsZUxvZ291dCIsImhlYWRlciIsImNsYXNzTmFtZSIsImRpdiIsImJ1dHRvbiIsInR5cGUiLCJvbkNsaWNrIiwiYXJpYS1sYWJlbCIsInNwYW4iLCJuYXYiLCJtYXAiLCJpdGVtIiwiYXMiLCJCdXR0b24iLCJwIiwiZnVsbF9uYW1lIiwiZW1haWwiLCJlbnRlciIsImVudGVyRnJvbSIsImVudGVyVG8iLCJsZWF2ZSIsImxlYXZlRnJvbSIsImxlYXZlVG8iLCJJdGVtcyIsIkl0ZW0iLCJhY3RpdmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Layout/Header.tsx\n");

/***/ }),

/***/ "./components/Layout/Layout.tsx":
/*!**************************************!*\
  !*** ./components/Layout/Layout.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"./components/Layout/Header.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Sidebar */ \"./components/Layout/Sidebar.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Footer */ \"./components/Layout/Footer.tsx\");\n/* harmony import */ var _UI_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../UI/LoadingSpinner */ \"./components/UI/LoadingSpinner.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_5__, _Sidebar__WEBPACK_IMPORTED_MODULE_6__, _UI_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_5__, _Sidebar__WEBPACK_IMPORTED_MODULE_6__, _UI_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * Main Layout component for Elderly Health Support System\n */ \n\n\n\n\n\n\n\n\nconst Layout = ({ children, title = \"Hệ thống hỗ trợ sức khỏe người cao tuổi\", description = \"Theo d\\xf5i v\\xe0 chăm s\\xf3c sức khỏe cho người cao tuổi\", showSidebar = true, className = \"\" })=>{\n    const { user, isLoading } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-elderly-bg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UI_LoadingSpinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: \"/og-image.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: \"/og-image.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        as: \"style\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-elderly-bg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#main-content\",\n                        className: \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-lg z-50\",\n                        children: \"Chuyển đến nội dung ch\\xednh\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            showSidebar && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                                className: \"hidden lg:block w-64 bg-white shadow-sm border-r border-elderly-border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                id: \"main-content\",\n                                className: `flex-1 ${showSidebar && user ? \"lg:ml-0\" : \"\"} ${className}`,\n                                role: \"main\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-screen\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 5000,\n                            style: {\n                                fontSize: \"16px\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\"\n                            },\n                            success: {\n                                iconTheme: {\n                                    primary: \"#10b981\",\n                                    secondary: \"#ffffff\"\n                                }\n                            },\n                            error: {\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#ffffff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout/Layout.tsx\n");

/***/ }),

/***/ "./components/Layout/Sidebar.tsx":
/*!***************************************!*\
  !*** ./components/Layout/Sidebar.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,HeartIcon,HomeIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Trang chủ\",\n        href: \"/\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.HomeIcon\n    },\n    {\n        name: \"Sức khỏe\",\n        href: \"/health\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.HeartIcon\n    },\n    {\n        name: \"Thuốc\",\n        href: \"/medications\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.BeakerIcon\n    },\n    {\n        name: \"Lịch hẹn\",\n        href: \"/schedules\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CalendarIcon\n    },\n    {\n        name: \"Tư vấn AI\",\n        href: \"/chat\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChatBubbleLeftRightIcon\n    },\n    {\n        name: \"Hồ sơ\",\n        href: \"/profile\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserIcon\n    },\n    {\n        name: \"C\\xe0i đặt\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_BeakerIcon_CalendarIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_HeartIcon_HomeIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.Cog6ToothIcon\n    }\n];\nconst Sidebar = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"flex-1 px-4 py-6 space-y-2\",\n            children: navigation.map((item)=>{\n                const isActive = router.pathname === item.href;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: item.href,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors\", isActive ? \"bg-primary-100 text-primary-700\" : \"text-elderly-text hover:bg-elderly-hover-bg hover:text-primary-600\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                            className: \"mr-3 h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 15\n                        }, undefined),\n                        item.name\n                    ]\n                }, item.name, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 13\n                }, undefined);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Sidebar.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout/Sidebar.tsx\n");

/***/ }),

/***/ "./components/UI/LoadingSpinner.tsx":
/*!******************************************!*\
  !*** ./components/UI/LoadingSpinner.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst LoadingSpinner = ({ size = \"md\", className })=>{\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        md: \"h-8 w-8\",\n        lg: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"loading-spinner\", sizeClasses[size], className)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\components\\\\UI\\\\LoadingSpinner.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSpinner);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1VJL0xvYWRpbmdTcGlubmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQTBCO0FBQ087QUFPakMsTUFBTUUsaUJBQWdELENBQUMsRUFDckRDLE9BQU8sSUFBSSxFQUNYQyxTQUFTLEVBQ1Y7SUFDQyxNQUFNQyxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVdILDhDQUFFQSxDQUFDLG1CQUFtQkksV0FBVyxDQUFDRixLQUFLLEVBQUVDOzs7Ozs7QUFFN0Q7QUFFQSxpRUFBZUYsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2VsZGVybHktaGVhbHRoLWZyb250ZW5kLy4vY29tcG9uZW50cy9VSS9Mb2FkaW5nU3Bpbm5lci50c3g/ODA2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cbmludGVyZmFjZSBMb2FkaW5nU3Bpbm5lclByb3BzIHtcbiAgc2l6ZT86ICdzbScgfCAnbWQnIHwgJ2xnJztcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5jb25zdCBMb2FkaW5nU3Bpbm5lcjogUmVhY3QuRkM8TG9hZGluZ1NwaW5uZXJQcm9wcz4gPSAoeyBcbiAgc2l6ZSA9ICdtZCcsIFxuICBjbGFzc05hbWUgXG59KSA9PiB7XG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtOiAnaC00IHctNCcsXG4gICAgbWQ6ICdoLTggdy04JyxcbiAgICBsZzogJ2gtMTIgdy0xMidcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbignbG9hZGluZy1zcGlubmVyJywgc2l6ZUNsYXNzZXNbc2l6ZV0sIGNsYXNzTmFtZSl9IC8+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBMb2FkaW5nU3Bpbm5lcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiTG9hZGluZ1NwaW5uZXIiLCJzaXplIiwiY2xhc3NOYW1lIiwic2l6ZUNsYXNzZXMiLCJzbSIsIm1kIiwibGciLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/UI/LoadingSpinner.tsx\n");

/***/ }),

/***/ "./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiUtils: () => (/* binding */ apiUtils),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   chatApi: () => (/* binding */ chatApi),\n/* harmony export */   dashboardApi: () => (/* binding */ dashboardApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   healthApi: () => (/* binding */ healthApi),\n/* harmony export */   medicationApi: () => (/* binding */ medicationApi),\n/* harmony export */   scheduleApi: () => (/* binding */ scheduleApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__, js_cookie__WEBPACK_IMPORTED_MODULE_1__]);\n([axios__WEBPACK_IMPORTED_MODULE_0__, js_cookie__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * API client for Elderly Health Support System\n */ \n\n// API configuration\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 10000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use(async (config)=>{\n        try {\n            // Get token from cookies (simple auth)\n            if (false) {}\n        } catch (error) {\n            console.warn(\"Failed to get auth token:\", error);\n        }\n        return config;\n    }, (error)=>{\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>response, (error)=>{\n        // Log error for debugging\n        console.error(\"API Error:\", {\n            status: error.response?.status,\n            statusText: error.response?.statusText,\n            data: error.response?.data,\n            url: error.config?.url,\n            method: error.config?.method,\n            headers: error.config?.headers\n        });\n        if (error.response?.status === 401) {\n            // Redirect to login on unauthorized\n            console.warn(\"401 Unauthorized - redirecting to login\");\n        // TEMPORARILY DISABLED FOR DEBUGGING\n        // if (typeof window !== 'undefined') {\n        //   Cookies.remove('auth_token');\n        //   window.location.href = '/auth/login';\n        // }\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\n// API client instance\nconst apiClient = createApiClient();\n// Generic API request function\nconst apiRequest = async (method, url, data, config)=>{\n    try {\n        const response = await apiClient.request({\n            method,\n            url,\n            data,\n            ...config\n        });\n        return response.data;\n    } catch (error) {\n        console.error(`API ${method} ${url} error:`, error);\n        throw new Error(error.response?.data?.message || error.message || \"An unexpected error occurred\");\n    }\n};\n// Auth API\nconst authApi = {\n    // Login\n    login: async (email, password)=>{\n        const response = await apiRequest(\"POST\", \"/auth/login\", {\n            email,\n            password\n        });\n        // Store token in cookies\n        if (false) {}\n        return response;\n    },\n    // Register\n    register: async (userData)=>{\n        const response = await apiRequest(\"POST\", \"/auth/register\", userData);\n        // Store token in cookies\n        if (false) {}\n        return response;\n    },\n    // Logout\n    logout: ()=>{\n        if (false) {}\n    },\n    // Check if user is authenticated\n    isAuthenticated: ()=>{\n        if (false) {}\n        return false;\n    }\n};\n// User API\nconst userApi = {\n    // Get current user profile\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/users/me\"),\n    // Create user profile\n    createUser: (userData)=>apiRequest(\"POST\", \"/users\", userData),\n    // Update user profile\n    updateUser: (userData)=>apiRequest(\"PUT\", \"/users/me\", userData),\n    // Get health profile\n    getHealthProfile: ()=>apiRequest(\"GET\", \"/users/me/health-profile\"),\n    // Create health profile\n    createHealthProfile: (profileData)=>apiRequest(\"POST\", \"/users/me/health-profile\", profileData),\n    // Update health profile\n    updateHealthProfile: (profileData)=>apiRequest(\"PUT\", \"/users/me/health-profile\", profileData),\n    // Get user settings\n    getSettings: ()=>apiRequest(\"GET\", \"/users/me/settings\"),\n    // Create/update user setting\n    updateSetting: (key, value)=>apiRequest(\"POST\", \"/users/me/settings\", {\n            setting_key: key,\n            setting_value: value\n        })\n};\n// Health Records API\nconst healthApi = {\n    // Get health records\n    getRecords: (params)=>apiRequest(\"GET\", \"/health/records\", undefined, {\n            params\n        }),\n    // Create health record\n    createRecord: (recordData)=>apiRequest(\"POST\", \"/health/records\", recordData),\n    // Get specific health record\n    getRecord: (recordId)=>apiRequest(\"GET\", `/health/records/${recordId}`),\n    // Delete health record\n    deleteRecord: (recordId)=>apiRequest(\"DELETE\", `/health/records/${recordId}`),\n    // Get health statistics\n    getStats: (recordType)=>apiRequest(\"GET\", \"/health/stats\", undefined, {\n            params: recordType ? {\n                record_type: recordType\n            } : undefined\n        })\n};\n// Medications API\nconst medicationApi = {\n    // Get medications\n    getMedications: (activeOnly = true)=>apiRequest(\"GET\", \"/medications\", undefined, {\n            params: {\n                active_only: activeOnly\n            }\n        }),\n    // Create medication\n    createMedication: (medicationData)=>apiRequest(\"POST\", \"/medications\", medicationData),\n    // Get specific medication\n    getMedication: (medicationId)=>apiRequest(\"GET\", `/medications/${medicationId}`),\n    // Update medication\n    updateMedication: (medicationId, medicationData)=>apiRequest(\"PUT\", `/medications/${medicationId}`, medicationData),\n    // Delete medication\n    deleteMedication: (medicationId)=>apiRequest(\"DELETE\", `/medications/${medicationId}`)\n};\n// Schedules API\nconst scheduleApi = {\n    // Get schedules\n    getSchedules: (params)=>apiRequest(\"GET\", \"/schedules\", undefined, {\n            params\n        }),\n    // Create schedule\n    createSchedule: (scheduleData)=>apiRequest(\"POST\", \"/schedules\", scheduleData),\n    // Get today's schedules\n    getTodaySchedules: ()=>apiRequest(\"GET\", \"/schedules/today\"),\n    // Get specific schedule\n    getSchedule: (scheduleId)=>apiRequest(\"GET\", `/schedules/${scheduleId}`),\n    // Update schedule\n    updateSchedule: (scheduleId, scheduleData)=>apiRequest(\"PUT\", `/schedules/${scheduleId}`, scheduleData),\n    // Delete schedule\n    deleteSchedule: (scheduleId)=>apiRequest(\"DELETE\", `/schedules/${scheduleId}`),\n    // Get reminders\n    getReminders: (params)=>apiRequest(\"GET\", \"/schedules/reminders\", undefined, {\n            params\n        }),\n    // Mark reminder as read\n    markReminderRead: (reminderId)=>apiRequest(\"PUT\", `/schedules/reminders/${reminderId}/read`)\n};\n// Chat API\nconst chatApi = {\n    // Create chat session\n    createSession: ()=>apiRequest(\"POST\", \"/chat/sessions\"),\n    // Get active session\n    getActiveSession: ()=>apiRequest(\"GET\", \"/chat/sessions/active\"),\n    // Send message\n    sendMessage: (sessionId, content)=>apiRequest(\"POST\", `/chat/sessions/${sessionId}/messages`, {\n            content\n        }),\n    // Get chat history\n    getChatHistory: (sessionId)=>apiRequest(\"GET\", `/chat/sessions/${sessionId}/messages`),\n    // End session\n    endSession: (sessionId)=>apiRequest(\"PUT\", `/chat/sessions/${sessionId}/end`)\n};\n// Dashboard API\nconst dashboardApi = {\n    // Get dashboard stats\n    getStats: ()=>apiRequest(\"GET\", \"/dashboard/stats\"),\n    // Get recent activity\n    getRecentActivity: (limit = 10)=>apiRequest(\"GET\", \"/dashboard/activity\", undefined, {\n            params: {\n                limit\n            }\n        }),\n    // Get health summary\n    getHealthSummary: ()=>apiRequest(\"GET\", \"/dashboard/health-summary\")\n};\n// Utility functions\nconst apiUtils = {\n    // Check API health\n    checkHealth: ()=>apiRequest(\"GET\", \"/health\"),\n    // Get API info\n    getInfo: ()=>apiRequest(\"GET\", \"/info\"),\n    // Upload file (if needed)\n    uploadFile: async (file, endpoint)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return apiRequest(\"POST\", endpoint, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n    }\n};\n// Export default API client\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api.ts\n");

/***/ }),

/***/ "./lib/auth.tsx":
/*!**********************!*\
  !*** ./lib/auth.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useUser: () => (/* binding */ useUser),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_2__, axios__WEBPACK_IMPORTED_MODULE_4__]);\n([js_cookie__WEBPACK_IMPORTED_MODULE_2__, axios__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * Simple authentication context and hooks\n */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            // Try to get token from cookie first, then localStorage\n            let savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"auth_token\");\n            if (!savedToken && \"undefined\" !== \"undefined\") {}\n            if (savedToken) {\n                setToken(savedToken);\n                try {\n                    // Verify token and get user info\n                    const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(`${API_BASE_URL}/auth/me`, {\n                        headers: {\n                            Authorization: `Bearer ${savedToken}`\n                        }\n                    });\n                    setUser(response.data);\n                    // Ensure token is saved in both places\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", savedToken, {\n                        expires: 7\n                    }); // 7 days\n                    if (false) {}\n                } catch (error) {\n                    console.error(\"Token verification failed:\", error);\n                    // Remove invalid token from both places\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n                    if (false) {}\n                    setToken(null);\n                }\n            }\n            setIsLoading(false);\n        };\n        initAuth();\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(`${API_BASE_URL}/auth/login`, {\n                email,\n                password\n            });\n            const { access_token, user: userData } = response.data;\n            // Save token to both cookie and localStorage\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", access_token, {\n                expires: 7\n            }); // 7 days\n            if (false) {}\n            setToken(access_token);\n            setUser(userData);\n            // Redirect to dashboard\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw new Error(error.response?.data?.detail || \"Login failed. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (email, password, full_name, phone)=>{\n        try {\n            setIsLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(`${API_BASE_URL}/auth/register`, {\n                email,\n                password,\n                full_name,\n                phone\n            });\n            const { access_token, user: userData } = response.data;\n            // Save token to both cookie and localStorage\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", access_token, {\n                expires: 7\n            }); // 7 days\n            if (false) {}\n            setToken(access_token);\n            setUser(userData);\n            // Redirect to dashboard\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Registration failed:\", error);\n            throw new Error(error.response?.data?.detail || \"Registration failed. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        // Remove token from both cookie and localStorage\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n        if (false) {}\n        setToken(null);\n        setUser(null);\n        // Redirect to home\n        router.push(\"/\");\n    };\n    const value = {\n        user,\n        isLoading,\n        login,\n        register,\n        logout,\n        token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n        lineNumber: 182,\n        columnNumber: 10\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// HOC for protected pages\nconst withAuth = (Component)=>{\n    return function AuthenticatedComponent(props) {\n        const { user, isLoading } = useAuth();\n        const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!isLoading && !user) {\n                router.push(\"/auth/login\");\n            }\n        }, [\n            user,\n            isLoading,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n                lineNumber: 209,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            return null; // Will redirect\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\lib\\\\auth.tsx\",\n            lineNumber: 219,\n            columnNumber: 12\n        }, this);\n    };\n};\n// Hook for checking if user is authenticated\nconst useUser = ()=>{\n    const { user, isLoading } = useAuth();\n    return {\n        user,\n        isLoading,\n        error: null\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/auth.tsx\n");

/***/ }),

/***/ "./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateAge: () => (/* binding */ calculateAge),\n/* harmony export */   calculateBMI: () => (/* binding */ calculateBMI),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   downloadJSON: () => (/* binding */ downloadJSON),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getBMICategory: () => (/* binding */ getBMICategory),\n/* harmony export */   getDateRange: () => (/* binding */ getDateRange),\n/* harmony export */   getHealthStatus: () => (/* binding */ getHealthStatus),\n/* harmony export */   getHealthStatusColor: () => (/* binding */ getHealthStatusColor),\n/* harmony export */   getHealthStatusMessage: () => (/* binding */ getHealthStatusMessage),\n/* harmony export */   getRecordTypeDisplayName: () => (/* binding */ getRecordTypeDisplayName),\n/* harmony export */   getRecordTypeUnit: () => (/* binding */ getRecordTypeUnit),\n/* harmony export */   isElderly: () => (/* binding */ isElderly),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhoneNumber: () => (/* binding */ isValidPhoneNumber),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\n/* harmony import */ var _barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!date-fns */ \"__barrel_optimize__?names=addDays,differenceInYears,endOfDay,format,isValid,parseISO,startOfDay!=!./node_modules/date-fns/esm/index.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! date-fns/locale */ \"date-fns/locale\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(date_fns_locale__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * Utility functions for Elderly Health Support System\n */ \n\n\n\n/**\n * Combine class names with Tailwind CSS merge\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format date for display\n */ function formatDate(date, formatStr = \"dd/MM/yyyy\") {\n    try {\n        const dateObj = typeof date === \"string\" ? (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isValid)(dateObj)) return \"Ng\\xe0y kh\\xf4ng hợp lệ\";\n        return (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)(dateObj, formatStr, {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_2__.vi\n        });\n    } catch (error) {\n        console.error(\"Error formatting date:\", error);\n        return \"Ng\\xe0y kh\\xf4ng hợp lệ\";\n    }\n}\n/**\n * Format datetime for display\n */ function formatDateTime(date) {\n    return formatDate(date, \"dd/MM/yyyy HH:mm\");\n}\n/**\n * Format time for display\n */ function formatTime(date) {\n    return formatDate(date, \"HH:mm\");\n}\n/**\n * Calculate age from date of birth\n */ function calculateAge(dateOfBirth) {\n    try {\n        const birthDate = typeof dateOfBirth === \"string\" ? (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.parseISO)(dateOfBirth) : dateOfBirth;\n        if (!(0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isValid)(birthDate)) return 0;\n        return (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.differenceInYears)(new Date(), birthDate);\n    } catch (error) {\n        console.error(\"Error calculating age:\", error);\n        return 0;\n    }\n}\n/**\n * Format phone number for display\n */ function formatPhoneNumber(phone) {\n    if (!phone) return \"\";\n    // Remove all non-digits\n    const cleaned = phone.replace(/\\D/g, \"\");\n    // Format Vietnamese phone numbers\n    if (cleaned.length === 10 && cleaned.startsWith(\"0\")) {\n        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;\n    }\n    if (cleaned.length === 11 && cleaned.startsWith(\"84\")) {\n        return `+84 ${cleaned.slice(2, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`;\n    }\n    return phone;\n}\n/**\n * Validate email address\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate Vietnamese phone number\n */ function isValidPhoneNumber(phone) {\n    const phoneRegex = /^(\\+84|84|0)(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-6|8|9]|9[0-4|6-9])[0-9]{7}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n}\n/**\n * Get health status based on record type and value\n */ function getHealthStatus(record) {\n    if (!record) return \"unknown\";\n    switch(record.record_type){\n        case \"blood_pressure\":\n            if (!record.systolic_pressure || !record.diastolic_pressure) return \"unknown\";\n            if (record.systolic_pressure >= 180 || record.diastolic_pressure >= 110) return \"danger\";\n            if (record.systolic_pressure >= 140 || record.diastolic_pressure >= 90) return \"warning\";\n            if (record.systolic_pressure >= 120 || record.diastolic_pressure >= 80) return \"warning\";\n            return \"normal\";\n        case \"heart_rate\":\n            if (!record.heart_rate) return \"unknown\";\n            if (record.heart_rate < 50 || record.heart_rate > 120) return \"danger\";\n            if (record.heart_rate < 60 || record.heart_rate > 100) return \"warning\";\n            return \"normal\";\n        case \"blood_sugar\":\n            if (!record.blood_sugar) return \"unknown\";\n            if (record.blood_sugar < 50 || record.blood_sugar > 300) return \"danger\";\n            if (record.blood_sugar < 70 || record.blood_sugar > 180) return \"warning\";\n            return \"normal\";\n        case \"temperature\":\n            if (!record.temperature) return \"unknown\";\n            if (record.temperature < 35 || record.temperature > 39) return \"danger\";\n            if (record.temperature < 36 || record.temperature > 37.5) return \"warning\";\n            return \"normal\";\n        case \"weight\":\n            return \"normal\"; // Weight doesn't have universal normal ranges\n        default:\n            return \"unknown\";\n    }\n}\n/**\n * Get health status color\n */ function getHealthStatusColor(status) {\n    switch(status){\n        case \"normal\":\n            return \"text-green-600 bg-green-100\";\n        case \"warning\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"danger\":\n            return \"text-red-600 bg-red-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\n/**\n * Get health status message\n */ function getHealthStatusMessage(status, recordType) {\n    const messages = {\n        normal: {\n            blood_pressure: \"Huyết \\xe1p b\\xecnh thường\",\n            heart_rate: \"Nhịp tim b\\xecnh thường\",\n            blood_sugar: \"Đường huyết b\\xecnh thường\",\n            weight: \"C\\xe2n nặng ổn định\",\n            temperature: \"Nhiệt độ b\\xecnh thường\"\n        },\n        warning: {\n            blood_pressure: \"Huyết \\xe1p hơi cao, cần theo d\\xf5i\",\n            heart_rate: \"Nhịp tim bất thường, cần ch\\xfa \\xfd\",\n            blood_sugar: \"Đường huyết cao, cần kiểm so\\xe1t\",\n            weight: \"C\\xe2n nặng thay đổi\",\n            temperature: \"Nhiệt độ hơi cao\"\n        },\n        danger: {\n            blood_pressure: \"Huyết \\xe1p rất cao, cần kh\\xe1m ngay\",\n            heart_rate: \"Nhịp tim bất thường nghi\\xeam trọng\",\n            blood_sugar: \"Đường huyết nguy hiểm\",\n            weight: \"C\\xe2n nặng thay đổi đ\\xe1ng lo\",\n            temperature: \"Sốt cao, cần chăm s\\xf3c y tế\"\n        },\n        unknown: {\n            blood_pressure: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\",\n            heart_rate: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\",\n            blood_sugar: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\",\n            weight: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\",\n            temperature: \"Kh\\xf4ng thể đ\\xe1nh gi\\xe1\"\n        }\n    };\n    return messages[status][recordType] || \"Kh\\xf4ng x\\xe1c định\";\n}\n/**\n * Get record type display name\n */ function getRecordTypeDisplayName(recordType) {\n    const displayNames = {\n        blood_pressure: \"Huyết \\xe1p\",\n        heart_rate: \"Nhịp tim\",\n        blood_sugar: \"Đường huyết\",\n        weight: \"C\\xe2n nặng\",\n        temperature: \"Nhiệt độ\"\n    };\n    return displayNames[recordType] || recordType;\n}\n/**\n * Get record type unit\n */ function getRecordTypeUnit(recordType) {\n    const units = {\n        blood_pressure: \"mmHg\",\n        heart_rate: \"bpm\",\n        blood_sugar: \"mg/dL\",\n        weight: \"kg\",\n        temperature: \"\\xb0C\"\n    };\n    return units[recordType] || \"\";\n}\n/**\n * Format number with locale\n */ function formatNumber(value, decimals = 1) {\n    return new Intl.NumberFormat(\"vi-VN\", {\n        minimumFractionDigits: decimals,\n        maximumFractionDigits: decimals\n    }).format(value);\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Throttle function\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * Generate random ID\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (error) {\n        console.error(\"Failed to copy to clipboard:\", error);\n        return false;\n    }\n}\n/**\n * Download data as JSON file\n */ function downloadJSON(data, filename) {\n    const blob = new Blob([\n        JSON.stringify(data, null, 2)\n    ], {\n        type: \"application/json\"\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = `${filename}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n}\n/**\n * Get date range for filtering\n */ function getDateRange(period) {\n    const now = new Date();\n    const today = (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.startOfDay)(now);\n    switch(period){\n        case \"today\":\n            return {\n                start: today,\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n        case \"week\":\n            return {\n                start: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.addDays)(today, -7),\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n        case \"month\":\n            return {\n                start: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.addDays)(today, -30),\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n        case \"year\":\n            return {\n                start: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.addDays)(today, -365),\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n        default:\n            return {\n                start: today,\n                end: (0,_barrel_optimize_names_addDays_differenceInYears_endOfDay_format_isValid_parseISO_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.endOfDay)(now)\n            };\n    }\n}\n/**\n * Check if user is elderly (65+)\n */ function isElderly(age) {\n    return age >= 65;\n}\n/**\n * Get BMI category\n */ function getBMICategory(bmi) {\n    if (bmi < 18.5) return \"Thiếu c\\xe2n\";\n    if (bmi < 25) return \"B\\xecnh thường\";\n    if (bmi < 30) return \"Thừa c\\xe2n\";\n    return \"B\\xe9o ph\\xec\";\n}\n/**\n * Calculate BMI\n */ function calculateBMI(weight, height) {\n    if (!weight || !height || height === 0) return 0;\n    const heightInMeters = height / 100;\n    return weight / (heightInMeters * heightInMeters);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/utils.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query/devtools */ \"react-query/devtools\");\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_query_devtools__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/**\n * Next.js App component for Elderly Health Support System\n */ \n\n\n\n\n\n// Create a client\nconst createQueryClient = ()=>new react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n        defaultOptions: {\n            queries: {\n                retry: 1,\n                refetchOnWindowFocus: false,\n                staleTime: 5 * 60 * 1000,\n                cacheTime: 10 * 60 * 1000\n            },\n            mutations: {\n                retry: 1\n            }\n        }\n    });\nfunction App({ Component, pageProps }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(()=>createQueryClient());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider, {\n            client: queryClient,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query_devtools__WEBPACK_IMPORTED_MODULE_3__.ReactQueryDevtools, {\n                    initialIsOpen: false\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_app.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"vi\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2RDtBQUU5QyxTQUFTSTtJQUN0QixxQkFDRSw4REFBQ0osK0NBQUlBO1FBQUNLLE1BQUs7OzBCQUNULDhEQUFDSiwrQ0FBSUE7O2tDQUNILDhEQUFDSzt3QkFBS0MsU0FBUTs7Ozs7O2tDQUNkLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7O2tDQUN0Qiw4REFBQ0Y7d0JBQ0NFLE1BQUs7d0JBQ0xELEtBQUk7Ozs7Ozs7Ozs7OzswQkFHUiw4REFBQ0U7O2tDQUNDLDhEQUFDVCwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxkZXJseS1oZWFsdGgtZnJvbnRlbmQvLi9wYWdlcy9fZG9jdW1lbnQudHN4P2QzN2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSHRtbCwgSGVhZCwgTWFpbiwgTmV4dFNjcmlwdCB9IGZyb20gJ25leHQvZG9jdW1lbnQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEb2N1bWVudCgpIHtcbiAgcmV0dXJuIChcbiAgICA8SHRtbCBsYW5nPVwidmlcIj5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8bWV0YSBjaGFyU2V0PVwidXRmLThcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICAgIDxsaW5rXG4gICAgICAgICAgaHJlZj1cImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb20vY3NzMj9mYW1pbHk9SW50ZXI6d2dodEAzMDA7NDAwOzUwMDs2MDA7NzAwJmRpc3BsYXk9c3dhcFwiXG4gICAgICAgICAgcmVsPVwic3R5bGVzaGVldFwiXG4gICAgICAgIC8+XG4gICAgICA8L0hlYWQ+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPE1haW4gLz5cbiAgICAgICAgPE5leHRTY3JpcHQgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L0h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSHRtbCIsIkhlYWQiLCJNYWluIiwiTmV4dFNjcmlwdCIsIkRvY3VtZW50IiwibGFuZyIsIm1ldGEiLCJjaGFyU2V0IiwibGluayIsInJlbCIsImhyZWYiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _components_Dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Dashboard/Dashboard */ \"./components/Dashboard/Dashboard.tsx\");\n/* harmony import */ var _components_Landing_LandingPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Landing/LandingPage */ \"./components/Landing/LandingPage.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__, _components_Dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_4__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_2__, _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__, _components_Dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/**\n * Home page for Elderly Health Support System\n */ \n\n\n\n\n\nconst HomePage = ()=>{\n    const { user, isLoading } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\index.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\index.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Trang chủ - Hệ thống hỗ trợ sức khỏe người cao tuổi\",\n        description: \"Theo d\\xf5i v\\xe0 chăm s\\xf3c sức khỏe cho người cao tuổi một c\\xe1ch dễ d\\xe0ng v\\xe0 hiệu quả\",\n        showSidebar: !!user,\n        children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\index.tsx\",\n            lineNumber: 30,\n            columnNumber: 15\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Landing_LandingPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\index.tsx\",\n            lineNumber: 30,\n            columnNumber: 31\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\index.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "date-fns/locale":
/*!**********************************!*\
  !*** external "date-fns/locale" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("date-fns/locale");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-query":
/*!******************************!*\
  !*** external "react-query" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ "react-query/devtools":
/*!***************************************!*\
  !*** external "react-query/devtools" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query/devtools");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "js-cookie":
/*!****************************!*\
  !*** external "js-cookie" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/date-fns","vendor-chunks/@headlessui","vendor-chunks/@heroicons","vendor-chunks/@babel"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();