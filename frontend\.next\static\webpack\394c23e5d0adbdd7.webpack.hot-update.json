{"c": ["webpack"], "r": ["pages/health", "pages/medications", "pages/schedules", "pages/chat", "pages/profile"], "m": ["./components/Health/AddHealthRecordModal.tsx", "./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js", "./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCodeThue2025%5CSucKhoe%5Cfrontend%5Cpages%5Chealth%5Cindex.tsx&page=%2Fhealth!", "./pages/health/index.tsx", "__barrel_optimize__?names=ChartBarIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./components/Medications/AddMedicationModal.tsx", "./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCodeThue2025%5CSucKhoe%5Cfrontend%5Cpages%5Cmedications%5Cindex.tsx&page=%2Fmedications!", "./pages/medications/index.tsx", "__barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./components/Schedules/AddScheduleModal.tsx", "./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js", "./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCodeThue2025%5CSucKhoe%5Cfrontend%5Cpages%5Cschedules%5Cindex.tsx&page=%2Fschedules!", "./pages/schedules/index.tsx", "__barrel_optimize__?names=CalendarIcon,ClockIcon,MapPinIcon,PencilIcon,PhoneIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./components/AIMessage.tsx", "./node_modules/@heroicons/react/24/outline/esm/PaperAirplaneIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCodeThue2025%5CSucKhoe%5Cfrontend%5Cpages%5Cchat%5Cindex.tsx&page=%2Fchat!", "./pages/chat/index.tsx", "__barrel_optimize__?names=PaperAirplaneIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCodeThue2025%5CSucKhoe%5Cfrontend%5Cpages%5Cprofile%5Cindex.tsx&page=%2Fprofile!", "./pages/profile/index.tsx", "__barrel_optimize__?names=CheckCircleIcon,HeartIcon,PhoneIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}