{"c": ["pages/medications", "webpack"], "r": ["pages/index", "pages/auth/login"], "m": ["./components/Dashboard/Dashboard.tsx", "./components/Landing/LandingPage.tsx", "./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js", "./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCodeThue2025%5CSucKhoe%5Cfrontend%5Cpages%5Cindex.tsx&page=%2F!", "./pages/index.tsx", "__barrel_optimize__?names=BeakerIcon,CalendarIcon,ChartBarIcon,HeartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,ClockIcon,HeartIcon,ShieldCheckIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js", "./node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CCodeThue2025%5CSucKhoe%5Cfrontend%5Cpages%5Cauth%5Clogin.tsx&page=%2Fauth%2Flogin!", "./pages/auth/login.tsx", "__barrel_optimize__?names=EyeIcon,EyeSlashIcon,HeartIcon,LockClosedIcon,ShieldCheckIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}