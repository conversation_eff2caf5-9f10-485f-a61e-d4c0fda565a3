import React from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import {
  HomeIcon,
  HeartIcon,
  BeakerIcon,
  CalendarIcon,
  ChatBubbleLeftRightIcon,
  UserIcon,
  Cog6ToothIcon,
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

const navigation = [
  { name: "Trang chủ", href: "/", icon: HomeIcon },
  { name: "Sức khỏe", href: "/health", icon: HeartIcon },
  { name: "Thuố<PERSON>", href: "/medications", icon: BeakerIcon },
  { name: "<PERSON><PERSON><PERSON> hẹn", href: "/schedules", icon: CalendarIcon },
  { name: "Tư vấn AI", href: "/chat", icon: ChatBubbleLeftRightIcon },
  { name: "<PERSON><PERSON> sơ", href: "/profile", icon: UserIcon },
  // { name: 'Cài đặt', href: '/settings', icon: Cog6ToothIcon },
];

const Sidebar: React.FC = () => {
  const router = useRouter();

  return (
    <div className="flex flex-col h-full">
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = router.pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors",
                isActive
                  ? "bg-primary-100 text-primary-700"
                  : "text-elderly-text hover:bg-elderly-hover-bg hover:text-primary-600"
              )}
            >
              <item.icon className="mr-3 h-5 w-5" />
              {item.name}
            </Link>
          );
        })}
      </nav>
    </div>
  );
};

export default Sidebar;
