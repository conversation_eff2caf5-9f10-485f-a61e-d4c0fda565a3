"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/medications",{

/***/ "./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiUtils: function() { return /* binding */ apiUtils; },\n/* harmony export */   authApi: function() { return /* binding */ authApi; },\n/* harmony export */   chatApi: function() { return /* binding */ chatApi; },\n/* harmony export */   dashboardApi: function() { return /* binding */ dashboardApi; },\n/* harmony export */   healthApi: function() { return /* binding */ healthApi; },\n/* harmony export */   medicationApi: function() { return /* binding */ medicationApi; },\n/* harmony export */   scheduleApi: function() { return /* binding */ scheduleApi; },\n/* harmony export */   userApi: function() { return /* binding */ userApi; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/**\n * API client for Elderly Health Support System\n */ \n\n// API configuration\nconst API_BASE_URL = \"http://localhost:8001/api\" || 0;\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 10000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use(async (config)=>{\n        try {\n            // Get token from cookies (simple auth)\n            if (true) {\n                const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"auth_token\");\n                console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"\".concat(token.substring(0, 20), \"...\") : \"NO TOKEN\");\n                if (token) {\n                    // Decode token to check if it's valid\n                    try {\n                        const payload = JSON.parse(atob(token.split(\".\")[1]));\n                        console.log(\"\\uD83D\\uDD0D Token payload:\", payload);\n                        console.log(\"⏰ Token expires:\", new Date(payload.exp * 1000));\n                        console.log(\"\\uD83D\\uDD50 Current time:\", new Date());\n                        if (payload.exp * 1000 < Date.now()) {\n                            console.error(\"❌ Token has expired!\");\n                        } else {\n                            console.log(\"✅ Token is still valid\");\n                        }\n                    } catch (e) {\n                        console.error(\"❌ Failed to decode token:\", e);\n                    }\n                    config.headers.Authorization = \"Bearer \".concat(token);\n                    console.log(\"✅ Authorization header set\");\n                } else {\n                    console.warn(\"❌ No auth token found in cookies\");\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to get auth token:\", error);\n        }\n        return config;\n    }, (error)=>{\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>response, (error)=>{\n        var _error_response, _error_response1, _error_response2, _error_config, _error_config1, _error_config2, _error_response3;\n        // Log error for debugging\n        console.error(\"API Error:\", {\n            status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n            statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n            data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n            url: (_error_config = error.config) === null || _error_config === void 0 ? void 0 : _error_config.url,\n            method: (_error_config1 = error.config) === null || _error_config1 === void 0 ? void 0 : _error_config1.method,\n            headers: (_error_config2 = error.config) === null || _error_config2 === void 0 ? void 0 : _error_config2.headers\n        });\n        if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 401) {\n            // Redirect to login on unauthorized\n            console.warn(\"401 Unauthorized - redirecting to login\");\n        // TEMPORARILY DISABLED FOR DEBUGGING\n        // if (typeof window !== 'undefined') {\n        //   Cookies.remove('auth_token');\n        //   window.location.href = '/auth/login';\n        // }\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\n// API client instance\nconst apiClient = createApiClient();\n// Generic API request function\nconst apiRequest = async (method, url, data, config)=>{\n    try {\n        const response = await apiClient.request({\n            method,\n            url,\n            data,\n            ...config\n        });\n        return response.data;\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"API \".concat(method, \" \").concat(url, \" error:\"), error);\n        throw new Error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"An unexpected error occurred\");\n    }\n};\n// Auth API\nconst authApi = {\n    // Login\n    login: async (email, password)=>{\n        const response = await apiRequest(\"POST\", \"/auth/login\", {\n            email,\n            password\n        });\n        // Store token in cookies\n        if ( true && response.access_token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"auth_token\", response.access_token, {\n                expires: 1\n            });\n        }\n        return response;\n    },\n    // Register\n    register: async (userData)=>{\n        const response = await apiRequest(\"POST\", \"/auth/register\", userData);\n        // Store token in cookies\n        if ( true && response.access_token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"auth_token\", response.access_token, {\n                expires: 1\n            });\n        }\n        return response;\n    },\n    // Logout\n    logout: ()=>{\n        if (true) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"auth_token\");\n            window.location.href = \"/auth/login\";\n        }\n    },\n    // Check if user is authenticated\n    isAuthenticated: ()=>{\n        if (true) {\n            return !!js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"auth_token\");\n        }\n        return false;\n    }\n};\n// User API\nconst userApi = {\n    // Get current user profile\n    getCurrentUser: ()=>apiRequest(\"GET\", \"/users/me\"),\n    // Create user profile\n    createUser: (userData)=>apiRequest(\"POST\", \"/users\", userData),\n    // Update user profile\n    updateUser: (userData)=>apiRequest(\"PUT\", \"/users/me\", userData),\n    // Get health profile\n    getHealthProfile: ()=>apiRequest(\"GET\", \"/users/me/health-profile\"),\n    // Create health profile\n    createHealthProfile: (profileData)=>apiRequest(\"POST\", \"/users/me/health-profile\", profileData),\n    // Update health profile\n    updateHealthProfile: (profileData)=>apiRequest(\"PUT\", \"/users/me/health-profile\", profileData),\n    // Get user settings\n    getSettings: ()=>apiRequest(\"GET\", \"/users/me/settings\"),\n    // Create/update user setting\n    updateSetting: (key, value)=>apiRequest(\"POST\", \"/users/me/settings\", {\n            setting_key: key,\n            setting_value: value\n        })\n};\n// Health Records API\nconst healthApi = {\n    // Get health records\n    getRecords: (params)=>apiRequest(\"GET\", \"/health/records\", undefined, {\n            params\n        }),\n    // Create health record\n    createRecord: (recordData)=>apiRequest(\"POST\", \"/health/records\", recordData),\n    // Get specific health record\n    getRecord: (recordId)=>apiRequest(\"GET\", \"/health/records/\".concat(recordId)),\n    // Delete health record\n    deleteRecord: (recordId)=>apiRequest(\"DELETE\", \"/health/records/\".concat(recordId)),\n    // Get health statistics\n    getStats: (recordType)=>apiRequest(\"GET\", \"/health/stats\", undefined, {\n            params: recordType ? {\n                record_type: recordType\n            } : undefined\n        })\n};\n// Medications API\nconst medicationApi = {\n    // Get medications\n    getMedications: function() {\n        let activeOnly = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return apiRequest(\"GET\", \"/medications\", undefined, {\n            params: {\n                active_only: activeOnly\n            }\n        });\n    },\n    // Create medication\n    createMedication: (medicationData)=>apiRequest(\"POST\", \"/medications\", medicationData),\n    // Get specific medication\n    getMedication: (medicationId)=>apiRequest(\"GET\", \"/medications/\".concat(medicationId)),\n    // Update medication\n    updateMedication: (medicationId, medicationData)=>apiRequest(\"PUT\", \"/medications/\".concat(medicationId), medicationData),\n    // Delete medication\n    deleteMedication: (medicationId)=>apiRequest(\"DELETE\", \"/medications/\".concat(medicationId))\n};\n// Schedules API\nconst scheduleApi = {\n    // Get schedules\n    getSchedules: (params)=>apiRequest(\"GET\", \"/schedules\", undefined, {\n            params\n        }),\n    // Create schedule\n    createSchedule: (scheduleData)=>apiRequest(\"POST\", \"/schedules\", scheduleData),\n    // Get today's schedules\n    getTodaySchedules: ()=>apiRequest(\"GET\", \"/schedules/today\"),\n    // Get specific schedule\n    getSchedule: (scheduleId)=>apiRequest(\"GET\", \"/schedules/\".concat(scheduleId)),\n    // Update schedule\n    updateSchedule: (scheduleId, scheduleData)=>apiRequest(\"PUT\", \"/schedules/\".concat(scheduleId), scheduleData),\n    // Delete schedule\n    deleteSchedule: (scheduleId)=>apiRequest(\"DELETE\", \"/schedules/\".concat(scheduleId)),\n    // Get reminders\n    getReminders: (params)=>apiRequest(\"GET\", \"/schedules/reminders\", undefined, {\n            params\n        }),\n    // Mark reminder as read\n    markReminderRead: (reminderId)=>apiRequest(\"PUT\", \"/schedules/reminders/\".concat(reminderId, \"/read\"))\n};\n// Chat API\nconst chatApi = {\n    // Create chat session\n    createSession: ()=>apiRequest(\"POST\", \"/chat/sessions\"),\n    // Get active session\n    getActiveSession: ()=>apiRequest(\"GET\", \"/chat/sessions/active\"),\n    // Send message\n    sendMessage: (sessionId, content)=>apiRequest(\"POST\", \"/chat/sessions/\".concat(sessionId, \"/messages\"), {\n            content\n        }),\n    // Get chat history\n    getChatHistory: (sessionId)=>apiRequest(\"GET\", \"/chat/sessions/\".concat(sessionId, \"/messages\")),\n    // End session\n    endSession: (sessionId)=>apiRequest(\"PUT\", \"/chat/sessions/\".concat(sessionId, \"/end\"))\n};\n// Dashboard API\nconst dashboardApi = {\n    // Get dashboard stats\n    getStats: ()=>apiRequest(\"GET\", \"/dashboard/stats\"),\n    // Get recent activity\n    getRecentActivity: function() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return apiRequest(\"GET\", \"/dashboard/activity\", undefined, {\n            params: {\n                limit\n            }\n        });\n    },\n    // Get health summary\n    getHealthSummary: ()=>apiRequest(\"GET\", \"/dashboard/health-summary\")\n};\n// Utility functions\nconst apiUtils = {\n    // Check API health\n    checkHealth: ()=>apiRequest(\"GET\", \"/health\"),\n    // Get API info\n    getInfo: ()=>apiRequest(\"GET\", \"/info\"),\n    // Upload file (if needed)\n    uploadFile: async (file, endpoint)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return apiRequest(\"POST\", endpoint, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n    }\n};\n// Export default API client\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/api.ts\n"));

/***/ }),

/***/ "./pages/medications/index.tsx":
/*!*************************************!*\
  !*** ./pages/medications/index.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./lib/auth.tsx\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"./components/Layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ClockIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst MedicationsPage = ()=>{\n    _s();\n    const [medications, setMedications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMed, setEditingMed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInactive, setShowInactive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadMedications();\n    }, [\n        showInactive\n    ]);\n    const loadMedications = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.medicationsApi.getMedications(!showInactive);\n            setMedications(data);\n        } catch (err) {\n            console.error(\"Error loading medications:\", err);\n            setError(\"Kh\\xf4ng thể tải danh s\\xe1ch thuốc\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Bạn c\\xf3 chắc chắn muốn x\\xf3a thuốc n\\xe0y?\")) return;\n        try {\n            await medicationApi.deleteMedication(id);\n            await loadMedications();\n        } catch (err) {\n            console.error(\"Error deleting medication:\", err);\n            setError(\"Kh\\xf4ng thể x\\xf3a thuốc\");\n        }\n    };\n    const getStatusColor = (medication)=>{\n        if (!medication.is_active) {\n            return \"bg-gray-100 text-gray-800\";\n        }\n        if (medication.end_date) {\n            const endDate = new Date(medication.end_date);\n            const today = new Date();\n            const daysLeft = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            if (daysLeft <= 0) {\n                return \"bg-red-100 text-red-800\";\n            } else if (daysLeft <= 7) {\n                return \"bg-yellow-100 text-yellow-800\";\n            }\n        }\n        return \"bg-green-100 text-green-800\";\n    };\n    const getStatusText = (medication)=>{\n        if (!medication.is_active) {\n            return \"Đ\\xe3 ngừng\";\n        }\n        if (medication.end_date) {\n            const endDate = new Date(medication.end_date);\n            const today = new Date();\n            const daysLeft = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            if (daysLeft <= 0) {\n                return \"Hết hạn\";\n            } else if (daysLeft <= 7) {\n                return \"C\\xf2n \".concat(daysLeft, \" ng\\xe0y\");\n            }\n        }\n        return \"Đang d\\xf9ng\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"Quản l\\xfd thuốc\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Quản l\\xfd thuốc\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-elderly-text\",\n                            children: \"Quản l\\xfd thuốc\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowInactive(!showInactive),\n                                    className: \"btn \".concat(showInactive ? \"btn-secondary\" : \"btn-outline\"),\n                                    children: showInactive ? \"Ẩn thuốc đ\\xe3 ngừng\" : \"Hiện thuốc đ\\xe3 ngừng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddForm(true),\n                                    className: \"btn btn-primary flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PlusIcon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Th\\xeam thuốc mới\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadMedications,\n                            className: \"mt-2 text-red-600 hover:text-red-800 text-sm underline\",\n                            children: \"Thử lại\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: medications.length > 0 ? medications.map((medication)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-elderly-text\",\n                                                        children: medication.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 rounded-full text-sm \".concat(getStatusColor(medication)),\n                                                        children: getStatusText(medication)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-elderly-text-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Liều d\\xf9ng:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    medication.dosage\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-elderly-text-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Tần suất:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    medication.frequency\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-elderly-text-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Bắt đầu:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                        lineNumber: 188,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    new Date(medication.start_date).toLocaleDateString(\"vi-VN\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            medication.end_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-elderly-text-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Kết th\\xfac:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                        lineNumber: 195,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    new Date(medication.end_date).toLocaleDateString(\"vi-VN\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            medication.instructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-800 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ClockIcon, {\n                                                            className: \"h-4 w-4 inline mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Hướng dẫn:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        \" \",\n                                                        medication.instructions\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setEditingMed(medication),\n                                                className: \"p-2 text-blue-600 hover:bg-blue-50 rounded-lg\",\n                                                title: \"Chỉnh sửa\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PencilIcon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDelete(medication.id),\n                                                className: \"p-2 text-red-600 hover:bg-red-50 rounded-lg\",\n                                                title: \"X\\xf3a\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.TrashIcon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 17\n                            }, undefined)\n                        }, medication.id, false, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 15\n                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83D\\uDC8A\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-elderly-text mb-2\",\n                                children: showInactive ? \"Kh\\xf4ng c\\xf3 thuốc đ\\xe3 ngừng\" : \"Chưa c\\xf3 thuốc n\\xe0o\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-elderly-text-light mb-6\",\n                                children: showInactive ? \"Bạn chưa c\\xf3 thuốc n\\xe0o đ\\xe3 ngừng sử dụng\" : \"H\\xe3y th\\xeam thuốc đầu ti\\xean để bắt đầu theo d\\xf5i\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined),\n                            !showInactive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddForm(true),\n                                className: \"btn btn-primary\",\n                                children: \"Th\\xeam thuốc đầu ti\\xean\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined),\n                medications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-green-600\",\n                                    children: medications.filter((m)=>m.is_active).length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light\",\n                                    children: \"Thuốc đang d\\xf9ng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-yellow-600\",\n                                    children: medications.filter((m)=>{\n                                        if (!m.end_date || !m.is_active) return false;\n                                        const daysLeft = Math.ceil((new Date(m.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));\n                                        return daysLeft <= 7 && daysLeft > 0;\n                                    }).length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light\",\n                                    children: \"Sắp hết hạn\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-gray-600\",\n                                    children: medications.filter((m)=>!m.is_active).length\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elderly-text-light\",\n                                    children: \"Đ\\xe3 ngừng\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CodeThue2025\\\\SucKhoe\\\\frontend\\\\pages\\\\medications\\\\index.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MedicationsPage, \"B5C7c7rwEA2s2ja34ie9P6pPkH4=\");\n_c = MedicationsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(MedicationsPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"MedicationsPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/medications/index.tsx\n"));

/***/ })

});