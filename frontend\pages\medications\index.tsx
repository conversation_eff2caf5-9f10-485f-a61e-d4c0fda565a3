import React from "react";
import { withAuth } from "@/lib/auth";
import Layout from "@/components/Layout/Layout";

const MedicationsPage: React.FC = () => {
  return (
    <Layout title="Quản lý thuốc">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-elderly-text">
            Quản lý thuốc
          </h1>
          <button className="btn btn-primary">Thêm thuốc mới</button>
        </div>

        <div className="space-y-4">
          <div className="card">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold">Thu<PERSON>c huyết áp</h3>
                <p className="text-elderly-text-light">Lisinopril 10mg</p>
                <p className="text-sm text-elderly-text-light">
                  Uống 1 viên mỗi sáng
                </p>
              </div>
              <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                <PERSON>ang dùng
              </span>
            </div>
          </div>

          <div className="card">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold">Thuốc tiểu đường</h3>
                <p className="text-elderly-text-light">Metformin 500mg</p>
                <p className="text-sm text-elderly-text-light">
                  Uống 2 lần/ngày sau ăn
                </p>
              </div>
              <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                Đang dùng
              </span>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default withAuth(MedicationsPage);
